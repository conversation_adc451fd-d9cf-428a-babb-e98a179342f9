# Professional Admin Panel

A modern, professional admin panel built with React and Node.js, featuring role-based access control, user management, and a clean, responsive design.

## Features

### Backend Features

- **Authentication & Authorization**: JWT-based authentication with role-based access control
- **User Management**: Complete CRUD operations for user management
- **Role-Based Access**: Three user roles - Superadmin, Admin, and Agent
- **Security**: Password hashing, rate limiting, CORS protection, and security headers
- **Database**: MongoDB with Mongoose ODM
- **API Documentation**: RESTful API with proper error handling

### Frontend Features

- **Modern UI**: Professional design with SCSS styling
- **Responsive Design**: Mobile-friendly responsive layout
- **Role-Based Navigation**: Dynamic menu based on user permissions
- **Authentication**: Secure login with autofill options for testing
- **Dashboard**: Overview with statistics and recent activity
- **Sidebar Navigation**: Collapsible sidebar with toggle functionality

## Tech Stack

### Backend

- Node.js
- Express.js
- MongoDB with Mongoose
- JWT for authentication
- bcryptjs for password hashing
- CORS, Helmet for security
- Express Rate Limit
- Express Validator

### Frontend

- React 18
- React Router v6
- SCSS for styling
- Axios for API calls
- Context API for state management

## Project Structure

```
admin-panel-project/
├── backend/
│   ├── config/
│   │   └── database.js
│   ├── middleware/
│   │   ├── auth.js
│   │   └── errorHandler.js
│   ├── models/
│   │   └── User.js
│   ├── routes/
│   │   ├── auth.js
│   │   └── users.js
│   ├── scripts/
│   │   └── createSuperAdmin.js
│   ├── .env
│   ├── package.json
│   └── server.js
├── frontend/
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   │   └── Sidebar.jsx
│   │   ├── context/
│   │   │   └── AuthContext.js
│   │   ├── pages/
│   │   │   ├── Login.jsx
│   │   │   ├── Dashboard.jsx
│   │   │   ├── UserManagement.jsx
│   │   │   ├── Analytics.jsx
│   │   │   ├── Reports.jsx
│   │   │   ├── Settings.jsx
│   │   │   └── Profile.jsx
│   │   ├── services/
│   │   │   └── api.js
│   │   ├── styles/
│   │   │   ├── main.scss
│   │   │   └── login.scss
│   │   ├── App.jsx
│   │   └── index.js
│   ├── .env
│   └── package.json
└── README.md
```

## Installation & Setup

### Prerequisites

- Node.js (v14 or higher)
- MongoDB (local or cloud instance)
- npm or yarn

### Backend Setup

1. Navigate to the backend directory:

```bash
cd admin-panel-project/backend
```

2. Install dependencies:

```bash
npm install
```

3. Configure environment variables:

```bash
# Copy and edit the .env file
cp .env.example .env
```

Update the `.env` file with your configuration:

```env
PORT=5000
NODE_ENV=development
MONGODB_URI=mongodb://localhost:27017/admin_panel_db
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRE=7d
FRONTEND_URL=http://localhost:3000
```

4. Create the superadmin user:

```bash
npm run create-superadmin
```

5. Start the development server:

```bash
npm run dev
```

The backend will be running on `http://localhost:5000`

### Frontend Setup

1. Navigate to the frontend directory:

```bash
cd admin-panel-project/frontend
```

2. Install dependencies:

```bash
npm install
```

3. Start the development server:

```bash
npm start
```

The frontend will be running on `http://localhost:3000`

## Default Login Credentials

After running the `create-superadmin` script, you can use these credentials:

- **Username**: `superadmin`
- **Password**: `admin123`
- **Role**: Superadmin

The login page also provides quick autofill buttons for testing different user roles.

## API Endpoints

### Authentication

- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `PUT /api/auth/password` - Update password

### Users (Admin/Superadmin only)

- `GET /api/users` - Get all users (with pagination and search)
- `GET /api/users/:id` - Get single user
- `POST /api/users` - Create new user
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user (Superadmin only)
- `PATCH /api/users/:id/toggle-status` - Toggle user active status

### Health Check

- `GET /api/health` - Server health check

## User Roles & Permissions

### Superadmin

- Full access to all features
- Can manage all users including other admins
- Can delete users
- Access to all dashboard sections

### Admin

- Can manage agent users
- Cannot manage other admins or superadmins
- Access to user management and analytics
- Cannot delete users

### Agent

- Limited access to dashboard
- Can view analytics and reports
- Cannot access user management

## Development

### Running in Development Mode

Backend:

```bash
cd backend
npm run dev
```

Frontend:

```bash
cd frontend
npm start
```

### Building for Production

Frontend:

```bash
cd frontend
npm run build
```

Backend:

```bash
cd backend
npm start
```

## Security Features

- JWT token-based authentication
- Password hashing with bcryptjs
- Rate limiting to prevent brute force attacks
- CORS protection
- Security headers with Helmet
- Input validation and sanitization
- Role-based access control

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the ISC License.

## Support

For support or questions, please contact the development team.
