[{"/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/index.js": "1", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/reportWebVitals.js": "2", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/context/AuthContext.js": "3", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/services/api.js": "4", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Login.jsx": "5", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Dashboard.jsx": "6", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Sidebar.jsx": "7", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Profile.jsx": "8", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Reports.jsx": "9", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Settings.jsx": "10", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Analytics.jsx": "11", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/UserManagement.jsx": "12", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/App.jsx": "13"}, {"size": 517, "mtime": 1749806676269, "results": "14", "hashOfConfig": "15"}, {"size": 362, "mtime": 1749805634052, "results": "16", "hashOfConfig": "15"}, {"size": 6008, "mtime": 1749807323020, "results": "17", "hashOfConfig": "15"}, {"size": 2290, "mtime": 1749805998766, "results": "18", "hashOfConfig": "15"}, {"size": 6151, "mtime": 1749806415791, "results": "19", "hashOfConfig": "15"}, {"size": 9781, "mtime": 1749806110136, "results": "20", "hashOfConfig": "15"}, {"size": 3178, "mtime": 1749806075352, "results": "21", "hashOfConfig": "15"}, {"size": 11009, "mtime": 1749806621762, "results": "22", "hashOfConfig": "15"}, {"size": 2279, "mtime": 1749806554671, "results": "23", "hashOfConfig": "15"}, {"size": 8389, "mtime": 1749806581546, "results": "24", "hashOfConfig": "15"}, {"size": 3100, "mtime": 1749806542114, "results": "25", "hashOfConfig": "15"}, {"size": 11360, "mtime": 1749806523920, "results": "26", "hashOfConfig": "15"}, {"size": 3413, "mtime": 1749806651716, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "xxxguz", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/index.js", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/reportWebVitals.js", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/context/AuthContext.js", ["67"], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/services/api.js", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Login.jsx", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Dashboard.jsx", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Sidebar.jsx", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Profile.jsx", ["68"], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Reports.jsx", ["69"], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Settings.jsx", ["70"], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Analytics.jsx", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/UserManagement.jsx", ["71"], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/App.jsx", [], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "72", "line": 212, "column": 8, "nodeType": null}, {"ruleId": "73", "severity": 1, "message": "74", "line": 5, "column": 17, "nodeType": "75", "messageId": "76", "endLine": 5, "endColumn": 27}, {"ruleId": "73", "severity": 1, "message": "77", "line": 5, "column": 11, "nodeType": "75", "messageId": "76", "endLine": 5, "endColumn": 15}, {"ruleId": "73", "severity": 1, "message": "77", "line": 5, "column": 11, "nodeType": "75", "messageId": "76", "endLine": 5, "endColumn": 15}, {"ruleId": "78", "severity": 1, "message": "79", "line": 17, "column": 6, "nodeType": "80", "endLine": 17, "endColumn": 45, "suggestions": "81"}, "Parsing error: Identifier 'logout' has already been declared. (212:8)", "no-unused-vars", "'updateUser' is assigned a value but never used.", "Identifier", "unusedVar", "'user' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", "ArrayExpression", ["82"], {"desc": "83", "fix": "84"}, "Update the dependencies array to be: [currentPage, loadUsers, searchTerm, selectedRole]", {"range": "85", "text": "86"}, [612, 651], "[currentPage, loadUsers, searchTerm, selectedRole]"]