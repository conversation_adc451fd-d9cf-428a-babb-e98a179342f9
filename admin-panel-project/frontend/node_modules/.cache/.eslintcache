[{"/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/index.js": "1", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/App.js": "2", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/reportWebVitals.js": "3", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/context/AuthContext.js": "4", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Login.js": "5", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Sidebar.js": "6", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Dashboard.js": "7", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/services/api.js": "8"}, {"size": 513, "mtime": 1749806143420, "results": "9", "hashOfConfig": "10"}, {"size": 3918, "mtime": 1749806130872, "results": "11", "hashOfConfig": "10"}, {"size": 362, "mtime": 1749805634052, "results": "12", "hashOfConfig": "10"}, {"size": 5864, "mtime": 1749806029114, "results": "13", "hashOfConfig": "10"}, {"size": 6143, "mtime": 1749806058636, "results": "14", "hashOfConfig": "10"}, {"size": 3178, "mtime": 1749806075352, "results": "15", "hashOfConfig": "10"}, {"size": 9781, "mtime": 1749806110136, "results": "16", "hashOfConfig": "10"}, {"size": 2290, "mtime": 1749805998766, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "xxxguz", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/index.js", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/App.js", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/reportWebVitals.js", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/context/AuthContext.js", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Login.js", ["42"], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Sidebar.js", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Dashboard.js", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/services/api.js", [], [], {"ruleId": "43", "severity": 2, "message": "44", "line": 29, "column": 3, "nodeType": "45", "endLine": 29, "endColumn": 12}, "react-hooks/rules-of-hooks", "React Hook \"useEffect\" is called conditionally. React Hooks must be called in the exact same order in every component render.", "Identifier"]