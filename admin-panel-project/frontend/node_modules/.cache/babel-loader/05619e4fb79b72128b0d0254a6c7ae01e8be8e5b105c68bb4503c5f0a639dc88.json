{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { usersAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    activeUsers: 0,\n    adminUsers: 0,\n    agentUsers: 0\n  });\n  const [recentUsers, setRecentUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n\n      // Load users data for stats\n      const response = await usersAPI.getUsers({\n        limit: 10\n      });\n      if (response.success) {\n        const users = response.data;\n\n        // Calculate stats\n        const totalUsers = response.pagination.total;\n        const activeUsers = users.filter(u => u.isActive).length;\n        const adminUsers = users.filter(u => u.role === 'admin').length;\n        const agentUsers = users.filter(u => u.role === 'agent').length;\n        setStats({\n          totalUsers,\n          activeUsers,\n          adminUsers,\n          agentUsers\n        });\n        setRecentUsers(users.slice(0, 5));\n      }\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n  const getGreeting = () => {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Good morning';\n    if (hour < 18) return 'Good afternoon';\n    return 'Good evening';\n  };\n  const getRoleColor = role => {\n    switch (role) {\n      case 'superadmin':\n        return '#ef4444';\n      case 'admin':\n        return '#3b82f6';\n      case 'agent':\n        return '#10b981';\n      default:\n        return '#64748b';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '200px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Loading dashboard...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"content\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      style: {\n        marginBottom: '2rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: '700',\n            marginBottom: '0.5rem',\n            color: 'var(--text-primary)'\n          },\n          children: [getGreeting(), \", \", user === null || user === void 0 ? void 0 : user.name, \"! \\uD83D\\uDC4B\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: 'var(--text-secondary)',\n            fontSize: '1.1rem'\n          },\n          children: \"Welcome to your admin dashboard. Here's what's happening today.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n        gap: '1.5rem',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: 'var(--text-secondary)',\n                  fontSize: '0.875rem',\n                  marginBottom: '0.5rem'\n                },\n                children: \"Total Users\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: '700',\n                  color: 'var(--text-primary)',\n                  margin: 0\n                },\n                children: stats.totalUsers\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '2rem',\n                opacity: 0.6\n              },\n              children: \"\\uD83D\\uDC65\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: 'var(--text-secondary)',\n                  fontSize: '0.875rem',\n                  marginBottom: '0.5rem'\n                },\n                children: \"Active Users\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: '700',\n                  color: 'var(--success-color)',\n                  margin: 0\n                },\n                children: stats.activeUsers\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '2rem',\n                opacity: 0.6\n              },\n              children: \"\\u2705\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: 'var(--text-secondary)',\n                  fontSize: '0.875rem',\n                  marginBottom: '0.5rem'\n                },\n                children: \"Administrators\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: '700',\n                  color: 'var(--primary-color)',\n                  margin: 0\n                },\n                children: stats.adminUsers\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '2rem',\n                opacity: 0.6\n              },\n              children: \"\\uD83D\\uDC51\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: 'var(--text-secondary)',\n                  fontSize: '0.875rem',\n                  marginBottom: '0.5rem'\n                },\n                children: \"Agents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: '700',\n                  color: 'var(--warning-color)',\n                  margin: 0\n                },\n                children: stats.agentUsers\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '2rem',\n                opacity: 0.6\n              },\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), ((user === null || user === void 0 ? void 0 : user.role) === 'superadmin' || (user === null || user === void 0 ? void 0 : user.role) === 'admin') && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"card-title\",\n          children: \"Recent Users\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary btn-sm\",\n          onClick: () => window.location.href = '/users',\n          children: \"View All\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: recentUsers.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            overflowX: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            style: {\n              width: '100%',\n              borderCollapse: 'collapse'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                style: {\n                  borderBottom: '2px solid var(--border-color)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: '1rem',\n                    textAlign: 'left',\n                    color: 'var(--text-secondary)',\n                    fontWeight: '600'\n                  },\n                  children: \"User\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: '1rem',\n                    textAlign: 'left',\n                    color: 'var(--text-secondary)',\n                    fontWeight: '600'\n                  },\n                  children: \"Role\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: '1rem',\n                    textAlign: 'left',\n                    color: 'var(--text-secondary)',\n                    fontWeight: '600'\n                  },\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: '1rem',\n                    textAlign: 'left',\n                    color: 'var(--text-secondary)',\n                    fontWeight: '600'\n                  },\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: recentUsers.map(user => /*#__PURE__*/_jsxDEV(\"tr\", {\n                style: {\n                  borderBottom: '1px solid var(--border-color)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '1rem'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontWeight: '500',\n                        color: 'var(--text-primary)'\n                      },\n                      children: user.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.875rem',\n                        color: 'var(--text-secondary)'\n                      },\n                      children: user.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 211,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '1rem'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '9999px',\n                      fontSize: '0.75rem',\n                      fontWeight: '500',\n                      backgroundColor: getRoleColor(user.role) + '20',\n                      color: getRoleColor(user.role)\n                    },\n                    children: user.role\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '1rem'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '9999px',\n                      fontSize: '0.75rem',\n                      fontWeight: '500',\n                      backgroundColor: user.isActive ? 'var(--success-color)20' : 'var(--error-color)20',\n                      color: user.isActive ? 'var(--success-color)' : 'var(--error-color)'\n                    },\n                    children: user.isActive ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '1rem',\n                    color: 'var(--text-secondary)'\n                  },\n                  children: formatDate(user.createdAt)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 25\n                }, this)]\n              }, user._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '2rem',\n            color: 'var(--text-secondary)'\n          },\n          children: \"No users found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"YLfZlQs3IKB8j1EVIfJ5gzaGuoQ=\", false, function () {\n  return [useAuth];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "usersAPI", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "stats", "setStats", "totalUsers", "activeUsers", "adminUsers", "agentUsers", "recentUsers", "setRecentUsers", "loading", "setLoading", "loadDashboardData", "response", "getUsers", "limit", "success", "users", "data", "pagination", "total", "filter", "u", "isActive", "length", "role", "slice", "error", "console", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "getGreeting", "hour", "getHours", "getRoleColor", "className", "children", "style", "display", "justifyContent", "alignItems", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "fontSize", "fontWeight", "color", "name", "gridTemplateColumns", "gap", "margin", "opacity", "onClick", "window", "location", "href", "overflowX", "width", "borderCollapse", "borderBottom", "padding", "textAlign", "map", "email", "borderRadius", "backgroundColor", "createdAt", "_id", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { usersAPI } from '../services/api';\n\nconst Dashboard = () => {\n  const { user } = useAuth();\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    activeUsers: 0,\n    adminUsers: 0,\n    agentUsers: 0,\n  });\n  const [recentUsers, setRecentUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      \n      // Load users data for stats\n      const response = await usersAPI.getUsers({ limit: 10 });\n      \n      if (response.success) {\n        const users = response.data;\n        \n        // Calculate stats\n        const totalUsers = response.pagination.total;\n        const activeUsers = users.filter(u => u.isActive).length;\n        const adminUsers = users.filter(u => u.role === 'admin').length;\n        const agentUsers = users.filter(u => u.role === 'agent').length;\n        \n        setStats({\n          totalUsers,\n          activeUsers,\n          adminUsers,\n          agentUsers,\n        });\n        \n        setRecentUsers(users.slice(0, 5));\n      }\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    });\n  };\n\n  const getGreeting = () => {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Good morning';\n    if (hour < 18) return 'Good afternoon';\n    return 'Good evening';\n  };\n\n  const getRoleColor = (role) => {\n    switch (role) {\n      case 'superadmin':\n        return '#ef4444';\n      case 'admin':\n        return '#3b82f6';\n      case 'agent':\n        return '#10b981';\n      default:\n        return '#64748b';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"content\">\n        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>\n          <div>Loading dashboard...</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"content\">\n      {/* Welcome Section */}\n      <div className=\"card\" style={{ marginBottom: '2rem' }}>\n        <div className=\"card-body\">\n          <h1 style={{ fontSize: '2rem', fontWeight: '700', marginBottom: '0.5rem', color: 'var(--text-primary)' }}>\n            {getGreeting()}, {user?.name}! 👋\n          </h1>\n          <p style={{ color: 'var(--text-secondary)', fontSize: '1.1rem' }}>\n            Welcome to your admin dashboard. Here's what's happening today.\n          </p>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem', marginBottom: '2rem' }}>\n        <div className=\"card\">\n          <div className=\"card-body\">\n            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n              <div>\n                <p style={{ color: 'var(--text-secondary)', fontSize: '0.875rem', marginBottom: '0.5rem' }}>\n                  Total Users\n                </p>\n                <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--text-primary)', margin: 0 }}>\n                  {stats.totalUsers}\n                </p>\n              </div>\n              <div style={{ fontSize: '2rem', opacity: 0.6 }}>👥</div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"card-body\">\n            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n              <div>\n                <p style={{ color: 'var(--text-secondary)', fontSize: '0.875rem', marginBottom: '0.5rem' }}>\n                  Active Users\n                </p>\n                <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--success-color)', margin: 0 }}>\n                  {stats.activeUsers}\n                </p>\n              </div>\n              <div style={{ fontSize: '2rem', opacity: 0.6 }}>✅</div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"card-body\">\n            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n              <div>\n                <p style={{ color: 'var(--text-secondary)', fontSize: '0.875rem', marginBottom: '0.5rem' }}>\n                  Administrators\n                </p>\n                <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--primary-color)', margin: 0 }}>\n                  {stats.adminUsers}\n                </p>\n              </div>\n              <div style={{ fontSize: '2rem', opacity: 0.6 }}>👑</div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"card-body\">\n            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n              <div>\n                <p style={{ color: 'var(--text-secondary)', fontSize: '0.875rem', marginBottom: '0.5rem' }}>\n                  Agents\n                </p>\n                <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--warning-color)', margin: 0 }}>\n                  {stats.agentUsers}\n                </p>\n              </div>\n              <div style={{ fontSize: '2rem', opacity: 0.6 }}>🎯</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Recent Users */}\n      {(user?.role === 'superadmin' || user?.role === 'admin') && (\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h2 className=\"card-title\">Recent Users</h2>\n            <button \n              className=\"btn btn-primary btn-sm\"\n              onClick={() => window.location.href = '/users'}\n            >\n              View All\n            </button>\n          </div>\n          <div className=\"card-body\">\n            {recentUsers.length > 0 ? (\n              <div style={{ overflowX: 'auto' }}>\n                <table style={{ width: '100%', borderCollapse: 'collapse' }}>\n                  <thead>\n                    <tr style={{ borderBottom: '2px solid var(--border-color)' }}>\n                      <th style={{ padding: '1rem', textAlign: 'left', color: 'var(--text-secondary)', fontWeight: '600' }}>\n                        User\n                      </th>\n                      <th style={{ padding: '1rem', textAlign: 'left', color: 'var(--text-secondary)', fontWeight: '600' }}>\n                        Role\n                      </th>\n                      <th style={{ padding: '1rem', textAlign: 'left', color: 'var(--text-secondary)', fontWeight: '600' }}>\n                        Status\n                      </th>\n                      <th style={{ padding: '1rem', textAlign: 'left', color: 'var(--text-secondary)', fontWeight: '600' }}>\n                        Created\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {recentUsers.map((user) => (\n                      <tr key={user._id} style={{ borderBottom: '1px solid var(--border-color)' }}>\n                        <td style={{ padding: '1rem' }}>\n                          <div>\n                            <div style={{ fontWeight: '500', color: 'var(--text-primary)' }}>\n                              {user.name}\n                            </div>\n                            <div style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>\n                              {user.email}\n                            </div>\n                          </div>\n                        </td>\n                        <td style={{ padding: '1rem' }}>\n                          <span\n                            style={{\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '9999px',\n                              fontSize: '0.75rem',\n                              fontWeight: '500',\n                              backgroundColor: getRoleColor(user.role) + '20',\n                              color: getRoleColor(user.role),\n                            }}\n                          >\n                            {user.role}\n                          </span>\n                        </td>\n                        <td style={{ padding: '1rem' }}>\n                          <span\n                            style={{\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '9999px',\n                              fontSize: '0.75rem',\n                              fontWeight: '500',\n                              backgroundColor: user.isActive ? 'var(--success-color)20' : 'var(--error-color)20',\n                              color: user.isActive ? 'var(--success-color)' : 'var(--error-color)',\n                            }}\n                          >\n                            {user.isActive ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td style={{ padding: '1rem', color: 'var(--text-secondary)' }}>\n                          {formatDate(user.createdAt)}\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            ) : (\n              <div style={{ textAlign: 'center', padding: '2rem', color: 'var(--text-secondary)' }}>\n                No users found\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGN,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC;IACjCW,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdkB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAME,QAAQ,GAAG,MAAMjB,QAAQ,CAACkB,QAAQ,CAAC;QAAEC,KAAK,EAAE;MAAG,CAAC,CAAC;MAEvD,IAAIF,QAAQ,CAACG,OAAO,EAAE;QACpB,MAAMC,KAAK,GAAGJ,QAAQ,CAACK,IAAI;;QAE3B;QACA,MAAMd,UAAU,GAAGS,QAAQ,CAACM,UAAU,CAACC,KAAK;QAC5C,MAAMf,WAAW,GAAGY,KAAK,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,CAACC,MAAM;QACxD,MAAMlB,UAAU,GAAGW,KAAK,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACG,IAAI,KAAK,OAAO,CAAC,CAACD,MAAM;QAC/D,MAAMjB,UAAU,GAAGU,KAAK,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACG,IAAI,KAAK,OAAO,CAAC,CAACD,MAAM;QAE/DrB,QAAQ,CAAC;UACPC,UAAU;UACVC,WAAW;UACXC,UAAU;UACVC;QACF,CAAC,CAAC;QAEFE,cAAc,CAACQ,KAAK,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,IAAI,GAAG,IAAIN,IAAI,CAAC,CAAC,CAACO,QAAQ,CAAC,CAAC;IAClC,IAAID,IAAI,GAAG,EAAE,EAAE,OAAO,cAAc;IACpC,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,cAAc;EACvB,CAAC;EAED,MAAME,YAAY,GAAId,IAAI,IAAK;IAC7B,QAAQA,IAAI;MACV,KAAK,YAAY;QACf,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,IAAIf,OAAO,EAAE;IACX,oBACEZ,OAAA;MAAK0C,SAAS,EAAC,SAAS;MAAAC,QAAA,eACtB3C,OAAA;QAAK4C,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,UAAU,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAQ,CAAE;QAAAL,QAAA,eAC/F3C,OAAA;UAAA2C,QAAA,EAAK;QAAoB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEpD,OAAA;IAAK0C,SAAS,EAAC,SAAS;IAAAC,QAAA,gBAEtB3C,OAAA;MAAK0C,SAAS,EAAC,MAAM;MAACE,KAAK,EAAE;QAAES,YAAY,EAAE;MAAO,CAAE;MAAAV,QAAA,eACpD3C,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB3C,OAAA;UAAI4C,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEC,UAAU,EAAE,KAAK;YAAEF,YAAY,EAAE,QAAQ;YAAEG,KAAK,EAAE;UAAsB,CAAE;UAAAb,QAAA,GACtGL,WAAW,CAAC,CAAC,EAAC,IAAE,EAACnC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,IAAI,EAAC,gBAC/B;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpD,OAAA;UAAG4C,KAAK,EAAE;YAAEY,KAAK,EAAE,uBAAuB;YAAEF,QAAQ,EAAE;UAAS,CAAE;UAAAX,QAAA,EAAC;QAElE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpD,OAAA;MAAK4C,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEa,mBAAmB,EAAE,sCAAsC;QAAEC,GAAG,EAAE,QAAQ;QAAEN,YAAY,EAAE;MAAO,CAAE;MAAAV,QAAA,gBAChI3C,OAAA;QAAK0C,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YAAK4C,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAED,cAAc,EAAE;YAAgB,CAAE;YAAAH,QAAA,gBACrF3C,OAAA;cAAA2C,QAAA,gBACE3C,OAAA;gBAAG4C,KAAK,EAAE;kBAAEY,KAAK,EAAE,uBAAuB;kBAAEF,QAAQ,EAAE,UAAU;kBAAED,YAAY,EAAE;gBAAS,CAAE;gBAAAV,QAAA,EAAC;cAE5F;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJpD,OAAA;gBAAG4C,KAAK,EAAE;kBAAEU,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,KAAK;kBAAEC,KAAK,EAAE,qBAAqB;kBAAEI,MAAM,EAAE;gBAAE,CAAE;gBAAAjB,QAAA,EACxFvC,KAAK,CAACE;cAAU;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNpD,OAAA;cAAK4C,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEO,OAAO,EAAE;cAAI,CAAE;cAAAlB,QAAA,EAAC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpD,OAAA;QAAK0C,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YAAK4C,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAED,cAAc,EAAE;YAAgB,CAAE;YAAAH,QAAA,gBACrF3C,OAAA;cAAA2C,QAAA,gBACE3C,OAAA;gBAAG4C,KAAK,EAAE;kBAAEY,KAAK,EAAE,uBAAuB;kBAAEF,QAAQ,EAAE,UAAU;kBAAED,YAAY,EAAE;gBAAS,CAAE;gBAAAV,QAAA,EAAC;cAE5F;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJpD,OAAA;gBAAG4C,KAAK,EAAE;kBAAEU,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,KAAK;kBAAEC,KAAK,EAAE,sBAAsB;kBAAEI,MAAM,EAAE;gBAAE,CAAE;gBAAAjB,QAAA,EACzFvC,KAAK,CAACG;cAAW;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNpD,OAAA;cAAK4C,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEO,OAAO,EAAE;cAAI,CAAE;cAAAlB,QAAA,EAAC;YAAC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpD,OAAA;QAAK0C,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YAAK4C,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAED,cAAc,EAAE;YAAgB,CAAE;YAAAH,QAAA,gBACrF3C,OAAA;cAAA2C,QAAA,gBACE3C,OAAA;gBAAG4C,KAAK,EAAE;kBAAEY,KAAK,EAAE,uBAAuB;kBAAEF,QAAQ,EAAE,UAAU;kBAAED,YAAY,EAAE;gBAAS,CAAE;gBAAAV,QAAA,EAAC;cAE5F;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJpD,OAAA;gBAAG4C,KAAK,EAAE;kBAAEU,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,KAAK;kBAAEC,KAAK,EAAE,sBAAsB;kBAAEI,MAAM,EAAE;gBAAE,CAAE;gBAAAjB,QAAA,EACzFvC,KAAK,CAACI;cAAU;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNpD,OAAA;cAAK4C,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEO,OAAO,EAAE;cAAI,CAAE;cAAAlB,QAAA,EAAC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpD,OAAA;QAAK0C,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YAAK4C,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAED,cAAc,EAAE;YAAgB,CAAE;YAAAH,QAAA,gBACrF3C,OAAA;cAAA2C,QAAA,gBACE3C,OAAA;gBAAG4C,KAAK,EAAE;kBAAEY,KAAK,EAAE,uBAAuB;kBAAEF,QAAQ,EAAE,UAAU;kBAAED,YAAY,EAAE;gBAAS,CAAE;gBAAAV,QAAA,EAAC;cAE5F;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJpD,OAAA;gBAAG4C,KAAK,EAAE;kBAAEU,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,KAAK;kBAAEC,KAAK,EAAE,sBAAsB;kBAAEI,MAAM,EAAE;gBAAE,CAAE;gBAAAjB,QAAA,EACzFvC,KAAK,CAACK;cAAU;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNpD,OAAA;cAAK4C,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEO,OAAO,EAAE;cAAI,CAAE;cAAAlB,QAAA,EAAC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL,CAAC,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,MAAK,YAAY,IAAI,CAAAxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,MAAK,OAAO,kBACrD3B,OAAA;MAAK0C,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB3C,OAAA;QAAK0C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B3C,OAAA;UAAI0C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAY;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5CpD,OAAA;UACE0C,SAAS,EAAC,wBAAwB;UAClCoB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAS;UAAAtB,QAAA,EAChD;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNpD,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBjC,WAAW,CAACgB,MAAM,GAAG,CAAC,gBACrB1B,OAAA;UAAK4C,KAAK,EAAE;YAAEsB,SAAS,EAAE;UAAO,CAAE;UAAAvB,QAAA,eAChC3C,OAAA;YAAO4C,KAAK,EAAE;cAAEuB,KAAK,EAAE,MAAM;cAAEC,cAAc,EAAE;YAAW,CAAE;YAAAzB,QAAA,gBAC1D3C,OAAA;cAAA2C,QAAA,eACE3C,OAAA;gBAAI4C,KAAK,EAAE;kBAAEyB,YAAY,EAAE;gBAAgC,CAAE;gBAAA1B,QAAA,gBAC3D3C,OAAA;kBAAI4C,KAAK,EAAE;oBAAE0B,OAAO,EAAE,MAAM;oBAAEC,SAAS,EAAE,MAAM;oBAAEf,KAAK,EAAE,uBAAuB;oBAAED,UAAU,EAAE;kBAAM,CAAE;kBAAAZ,QAAA,EAAC;gBAEtG;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI4C,KAAK,EAAE;oBAAE0B,OAAO,EAAE,MAAM;oBAAEC,SAAS,EAAE,MAAM;oBAAEf,KAAK,EAAE,uBAAuB;oBAAED,UAAU,EAAE;kBAAM,CAAE;kBAAAZ,QAAA,EAAC;gBAEtG;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI4C,KAAK,EAAE;oBAAE0B,OAAO,EAAE,MAAM;oBAAEC,SAAS,EAAE,MAAM;oBAAEf,KAAK,EAAE,uBAAuB;oBAAED,UAAU,EAAE;kBAAM,CAAE;kBAAAZ,QAAA,EAAC;gBAEtG;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBAAI4C,KAAK,EAAE;oBAAE0B,OAAO,EAAE,MAAM;oBAAEC,SAAS,EAAE,MAAM;oBAAEf,KAAK,EAAE,uBAAuB;oBAAED,UAAU,EAAE;kBAAM,CAAE;kBAAAZ,QAAA,EAAC;gBAEtG;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRpD,OAAA;cAAA2C,QAAA,EACGjC,WAAW,CAAC8D,GAAG,CAAErE,IAAI,iBACpBH,OAAA;gBAAmB4C,KAAK,EAAE;kBAAEyB,YAAY,EAAE;gBAAgC,CAAE;gBAAA1B,QAAA,gBAC1E3C,OAAA;kBAAI4C,KAAK,EAAE;oBAAE0B,OAAO,EAAE;kBAAO,CAAE;kBAAA3B,QAAA,eAC7B3C,OAAA;oBAAA2C,QAAA,gBACE3C,OAAA;sBAAK4C,KAAK,EAAE;wBAAEW,UAAU,EAAE,KAAK;wBAAEC,KAAK,EAAE;sBAAsB,CAAE;sBAAAb,QAAA,EAC7DxC,IAAI,CAACsD;oBAAI;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,eACNpD,OAAA;sBAAK4C,KAAK,EAAE;wBAAEU,QAAQ,EAAE,UAAU;wBAAEE,KAAK,EAAE;sBAAwB,CAAE;sBAAAb,QAAA,EAClExC,IAAI,CAACsE;oBAAK;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACLpD,OAAA;kBAAI4C,KAAK,EAAE;oBAAE0B,OAAO,EAAE;kBAAO,CAAE;kBAAA3B,QAAA,eAC7B3C,OAAA;oBACE4C,KAAK,EAAE;sBACL0B,OAAO,EAAE,iBAAiB;sBAC1BI,YAAY,EAAE,QAAQ;sBACtBpB,QAAQ,EAAE,SAAS;sBACnBC,UAAU,EAAE,KAAK;sBACjBoB,eAAe,EAAElC,YAAY,CAACtC,IAAI,CAACwB,IAAI,CAAC,GAAG,IAAI;sBAC/C6B,KAAK,EAAEf,YAAY,CAACtC,IAAI,CAACwB,IAAI;oBAC/B,CAAE;oBAAAgB,QAAA,EAEDxC,IAAI,CAACwB;kBAAI;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLpD,OAAA;kBAAI4C,KAAK,EAAE;oBAAE0B,OAAO,EAAE;kBAAO,CAAE;kBAAA3B,QAAA,eAC7B3C,OAAA;oBACE4C,KAAK,EAAE;sBACL0B,OAAO,EAAE,iBAAiB;sBAC1BI,YAAY,EAAE,QAAQ;sBACtBpB,QAAQ,EAAE,SAAS;sBACnBC,UAAU,EAAE,KAAK;sBACjBoB,eAAe,EAAExE,IAAI,CAACsB,QAAQ,GAAG,wBAAwB,GAAG,sBAAsB;sBAClF+B,KAAK,EAAErD,IAAI,CAACsB,QAAQ,GAAG,sBAAsB,GAAG;oBAClD,CAAE;oBAAAkB,QAAA,EAEDxC,IAAI,CAACsB,QAAQ,GAAG,QAAQ,GAAG;kBAAU;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLpD,OAAA;kBAAI4C,KAAK,EAAE;oBAAE0B,OAAO,EAAE,MAAM;oBAAEd,KAAK,EAAE;kBAAwB,CAAE;kBAAAb,QAAA,EAC5DZ,UAAU,CAAC5B,IAAI,CAACyE,SAAS;gBAAC;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA,GAzCEjD,IAAI,CAAC0E,GAAG;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0Cb,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,gBAENpD,OAAA;UAAK4C,KAAK,EAAE;YAAE2B,SAAS,EAAE,QAAQ;YAAED,OAAO,EAAE,MAAM;YAAEd,KAAK,EAAE;UAAwB,CAAE;UAAAb,QAAA,EAAC;QAEtF;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClD,EAAA,CAjQID,SAAS;EAAA,QACIJ,OAAO;AAAA;AAAAiF,EAAA,GADpB7E,SAAS;AAmQf,eAAeA,SAAS;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}