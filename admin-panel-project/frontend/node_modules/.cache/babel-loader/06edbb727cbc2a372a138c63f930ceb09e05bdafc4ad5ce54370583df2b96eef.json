{"ast": null, "code": "import axios from 'axios';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle errors\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Token expired or invalid\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// Auth API\nexport const authAPI = {\n  login: async credentials => {\n    const response = await api.post('/auth/login', credentials);\n    return response.data;\n  },\n  getCurrentUser: async () => {\n    const response = await api.get('/auth/me');\n    return response.data;\n  },\n  updatePassword: async passwordData => {\n    const response = await api.put('/auth/password', passwordData);\n    return response.data;\n  }\n};\n\n// Users API\nexport const usersAPI = {\n  getUsers: async (params = {}) => {\n    const response = await api.get('/users', {\n      params\n    });\n    return response.data;\n  },\n  getUser: async id => {\n    const response = await api.get(`/users/${id}`);\n    return response.data;\n  },\n  createUser: async userData => {\n    const response = await api.post('/users', userData);\n    return response.data;\n  },\n  updateUser: async (id, userData) => {\n    const response = await api.put(`/users/${id}`, userData);\n    return response.data;\n  },\n  deleteUser: async id => {\n    const response = await api.delete(`/users/${id}`);\n    return response.data;\n  },\n  toggleUserStatus: async id => {\n    const response = await api.patch(`/users/${id}/toggle-status`);\n    return response.data;\n  }\n};\n\n// Health check\nexport const healthAPI = {\n  check: async () => {\n    const response = await api.get('/health');\n    return response.data;\n  }\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "authAPI", "login", "credentials", "post", "data", "getCurrentUser", "get", "updatePassword", "passwordData", "put", "usersAPI", "getUsers", "params", "getUser", "id", "createUser", "userData", "updateUser", "deleteUser", "delete", "toggleUserStatus", "patch", "healthAPI", "check"], "sources": ["/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle errors\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token expired or invalid\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth API\nexport const authAPI = {\n  login: async (credentials) => {\n    const response = await api.post('/auth/login', credentials);\n    return response.data;\n  },\n\n  getCurrentUser: async () => {\n    const response = await api.get('/auth/me');\n    return response.data;\n  },\n\n  updatePassword: async (passwordData) => {\n    const response = await api.put('/auth/password', passwordData);\n    return response.data;\n  },\n};\n\n// Users API\nexport const usersAPI = {\n  getUsers: async (params = {}) => {\n    const response = await api.get('/users', { params });\n    return response.data;\n  },\n\n  getUser: async (id) => {\n    const response = await api.get(`/users/${id}`);\n    return response.data;\n  },\n\n  createUser: async (userData) => {\n    const response = await api.post('/users', userData);\n    return response.data;\n  },\n\n  updateUser: async (id, userData) => {\n    const response = await api.put(`/users/${id}`, userData);\n    return response.data;\n  },\n\n  deleteUser: async (id) => {\n    const response = await api.delete(`/users/${id}`);\n    return response.data;\n  },\n\n  toggleUserStatus: async (id) => {\n    const response = await api.patch(`/users/${id}/toggle-status`);\n    return response.data;\n  },\n};\n\n// Health check\nexport const healthAPI = {\n  check: async () => {\n    const response = await api.get('/health');\n    return response.data;\n  },\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;EACrEC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAhB,GAAG,CAACQ,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;IAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMU,OAAO,GAAG;EACrBC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5B,MAAMT,QAAQ,GAAG,MAAMnB,GAAG,CAAC6B,IAAI,CAAC,aAAa,EAAED,WAAW,CAAC;IAC3D,OAAOT,QAAQ,CAACW,IAAI;EACtB,CAAC;EAEDC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,MAAMZ,QAAQ,GAAG,MAAMnB,GAAG,CAACgC,GAAG,CAAC,UAAU,CAAC;IAC1C,OAAOb,QAAQ,CAACW,IAAI;EACtB,CAAC;EAEDG,cAAc,EAAE,MAAOC,YAAY,IAAK;IACtC,MAAMf,QAAQ,GAAG,MAAMnB,GAAG,CAACmC,GAAG,CAAC,gBAAgB,EAAED,YAAY,CAAC;IAC9D,OAAOf,QAAQ,CAACW,IAAI;EACtB;AACF,CAAC;;AAED;AACA,OAAO,MAAMM,QAAQ,GAAG;EACtBC,QAAQ,EAAE,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;IAC/B,MAAMnB,QAAQ,GAAG,MAAMnB,GAAG,CAACgC,GAAG,CAAC,QAAQ,EAAE;MAAEM;IAAO,CAAC,CAAC;IACpD,OAAOnB,QAAQ,CAACW,IAAI;EACtB,CAAC;EAEDS,OAAO,EAAE,MAAOC,EAAE,IAAK;IACrB,MAAMrB,QAAQ,GAAG,MAAMnB,GAAG,CAACgC,GAAG,CAAC,UAAUQ,EAAE,EAAE,CAAC;IAC9C,OAAOrB,QAAQ,CAACW,IAAI;EACtB,CAAC;EAEDW,UAAU,EAAE,MAAOC,QAAQ,IAAK;IAC9B,MAAMvB,QAAQ,GAAG,MAAMnB,GAAG,CAAC6B,IAAI,CAAC,QAAQ,EAAEa,QAAQ,CAAC;IACnD,OAAOvB,QAAQ,CAACW,IAAI;EACtB,CAAC;EAEDa,UAAU,EAAE,MAAAA,CAAOH,EAAE,EAAEE,QAAQ,KAAK;IAClC,MAAMvB,QAAQ,GAAG,MAAMnB,GAAG,CAACmC,GAAG,CAAC,UAAUK,EAAE,EAAE,EAAEE,QAAQ,CAAC;IACxD,OAAOvB,QAAQ,CAACW,IAAI;EACtB,CAAC;EAEDc,UAAU,EAAE,MAAOJ,EAAE,IAAK;IACxB,MAAMrB,QAAQ,GAAG,MAAMnB,GAAG,CAAC6C,MAAM,CAAC,UAAUL,EAAE,EAAE,CAAC;IACjD,OAAOrB,QAAQ,CAACW,IAAI;EACtB,CAAC;EAEDgB,gBAAgB,EAAE,MAAON,EAAE,IAAK;IAC9B,MAAMrB,QAAQ,GAAG,MAAMnB,GAAG,CAAC+C,KAAK,CAAC,UAAUP,EAAE,gBAAgB,CAAC;IAC9D,OAAOrB,QAAQ,CAACW,IAAI;EACtB;AACF,CAAC;;AAED;AACA,OAAO,MAAMkB,SAAS,GAAG;EACvBC,KAAK,EAAE,MAAAA,CAAA,KAAY;IACjB,MAAM9B,QAAQ,GAAG,MAAMnB,GAAG,CAACgC,GAAG,CAAC,SAAS,CAAC;IACzC,OAAOb,QAAQ,CAACW,IAAI;EACtB;AACF,CAAC;AAED,eAAe9B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}