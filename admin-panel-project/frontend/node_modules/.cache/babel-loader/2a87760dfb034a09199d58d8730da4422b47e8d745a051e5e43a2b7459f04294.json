{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Reports.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Reports = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      style: {\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"card-title\",\n          children: \"Reports\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: 'var(--text-secondary)'\n          },\n          children: \"Generate and view various reports for your organization.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n        gap: '1.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"card-title\",\n            children: \"User Reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: 'var(--text-secondary)',\n              marginBottom: '1.5rem'\n            },\n            children: \"Generate reports about user activity, registrations, and engagement.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            children: \"Generate User Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"card-title\",\n            children: \"System Reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: 'var(--text-secondary)',\n              marginBottom: '1.5rem'\n            },\n            children: \"View system performance, uptime, and technical metrics.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            children: \"Generate System Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"card-title\",\n            children: \"Activity Reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: 'var(--text-secondary)',\n              marginBottom: '1.5rem'\n            },\n            children: \"Track user activities, login patterns, and usage statistics.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            children: \"Generate Activity Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_s(Reports, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c = Reports;\nexport default Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");", "map": {"version": 3, "names": ["React", "useAuth", "jsxDEV", "_jsxDEV", "Reports", "_s", "user", "style", "padding", "children", "className", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "display", "gridTemplateColumns", "gap", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Reports.jsx"], "sourcesContent": ["import React from 'react';\nimport { useAuth } from '../context/AuthContext';\n\nconst Reports = () => {\n  const { user } = useAuth();\n\n  return (\n    <div style={{ padding: '2rem' }}>\n      {/* Header */}\n      <div className=\"card\" style={{ marginBottom: '2rem' }}>\n        <div className=\"card-header\">\n          <h1 className=\"card-title\">Reports</h1>\n        </div>\n        <div className=\"card-body\">\n          <p style={{ color: 'var(--text-secondary)' }}>\n            Generate and view various reports for your organization.\n          </p>\n        </div>\n      </div>\n\n      {/* Report Types */}\n      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1.5rem' }}>\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"card-title\">User Reports</h3>\n          </div>\n          <div className=\"card-body\">\n            <p style={{ color: 'var(--text-secondary)', marginBottom: '1.5rem' }}>\n              Generate reports about user activity, registrations, and engagement.\n            </p>\n            <button className=\"btn btn-primary\">\n              Generate User Report\n            </button>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"card-title\">System Reports</h3>\n          </div>\n          <div className=\"card-body\">\n            <p style={{ color: 'var(--text-secondary)', marginBottom: '1.5rem' }}>\n              View system performance, uptime, and technical metrics.\n            </p>\n            <button className=\"btn btn-primary\">\n              Generate System Report\n            </button>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"card-title\">Activity Reports</h3>\n          </div>\n          <div className=\"card-body\">\n            <p style={{ color: 'var(--text-secondary)', marginBottom: '1.5rem' }}>\n              Track user activities, login patterns, and usage statistics.\n            </p>\n            <button className=\"btn btn-primary\">\n              Generate Activity Report\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Reports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM;IAAEC;EAAK,CAAC,GAAGL,OAAO,CAAC,CAAC;EAE1B,oBACEE,OAAA;IAAKI,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAE9BN,OAAA;MAAKO,SAAS,EAAC,MAAM;MAACH,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACpDN,OAAA;QAAKO,SAAS,EAAC,aAAa;QAAAD,QAAA,eAC1BN,OAAA;UAAIO,SAAS,EAAC,YAAY;UAAAD,QAAA,EAAC;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eACNZ,OAAA;QAAKO,SAAS,EAAC,WAAW;QAAAD,QAAA,eACxBN,OAAA;UAAGI,KAAK,EAAE;YAAES,KAAK,EAAE;UAAwB,CAAE;UAAAP,QAAA,EAAC;QAE9C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNZ,OAAA;MAAKI,KAAK,EAAE;QAAEU,OAAO,EAAE,MAAM;QAAEC,mBAAmB,EAAE,sCAAsC;QAAEC,GAAG,EAAE;MAAS,CAAE;MAAAV,QAAA,gBAC1GN,OAAA;QAAKO,SAAS,EAAC,MAAM;QAAAD,QAAA,gBACnBN,OAAA;UAAKO,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC1BN,OAAA;YAAIO,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAY;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNZ,OAAA;UAAKO,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBN,OAAA;YAAGI,KAAK,EAAE;cAAES,KAAK,EAAE,uBAAuB;cAAEL,YAAY,EAAE;YAAS,CAAE;YAAAF,QAAA,EAAC;UAEtE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJZ,OAAA;YAAQO,SAAS,EAAC,iBAAiB;YAAAD,QAAA,EAAC;UAEpC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENZ,OAAA;QAAKO,SAAS,EAAC,MAAM;QAAAD,QAAA,gBACnBN,OAAA;UAAKO,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC1BN,OAAA;YAAIO,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNZ,OAAA;UAAKO,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBN,OAAA;YAAGI,KAAK,EAAE;cAAES,KAAK,EAAE,uBAAuB;cAAEL,YAAY,EAAE;YAAS,CAAE;YAAAF,QAAA,EAAC;UAEtE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJZ,OAAA;YAAQO,SAAS,EAAC,iBAAiB;YAAAD,QAAA,EAAC;UAEpC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENZ,OAAA;QAAKO,SAAS,EAAC,MAAM;QAAAD,QAAA,gBACnBN,OAAA;UAAKO,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC1BN,OAAA;YAAIO,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAgB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACNZ,OAAA;UAAKO,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBN,OAAA;YAAGI,KAAK,EAAE;cAAES,KAAK,EAAE,uBAAuB;cAAEL,YAAY,EAAE;YAAS,CAAE;YAAAF,QAAA,EAAC;UAEtE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJZ,OAAA;YAAQO,SAAS,EAAC,iBAAiB;YAAAD,QAAA,EAAC;UAEpC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACV,EAAA,CA/DID,OAAO;EAAA,QACMH,OAAO;AAAA;AAAAmB,EAAA,GADpBhB,OAAO;AAiEb,eAAeA,OAAO;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}