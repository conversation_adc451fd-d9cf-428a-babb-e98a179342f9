{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Settings.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Settings = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('general');\n  const tabs = [{\n    id: 'general',\n    label: 'General',\n    icon: '⚙️'\n  }, {\n    id: 'security',\n    label: 'Security',\n    icon: '🔒'\n  }, {\n    id: 'notifications',\n    label: 'Notifications',\n    icon: '🔔'\n  }, {\n    id: 'appearance',\n    label: 'Appearance',\n    icon: '🎨'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      style: {\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"card-title\",\n          children: \"Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: 'var(--text-secondary)'\n          },\n          children: \"Manage your application settings and preferences.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: '250px 1fr',\n        gap: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          style: {\n            padding: '1rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"nav\", {\n            children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab(tab.id),\n              style: {\n                width: '100%',\n                padding: '0.75rem 1rem',\n                border: 'none',\n                background: activeTab === tab.id ? 'var(--primary-color)' : 'transparent',\n                color: activeTab === tab.id ? 'white' : 'var(--text-primary)',\n                borderRadius: '8px',\n                marginBottom: '0.5rem',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                textAlign: 'left',\n                transition: 'all 0.2s ease'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: tab.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 19\n              }, this), tab.label]\n            }, tab.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [activeTab === 'general' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '1.5rem'\n              },\n              children: \"General Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Application Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"form-control\",\n                defaultValue: \"Admin Panel\",\n                disabled: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Time Zone\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-control\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"UTC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"America/New_York\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 81,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"Europe/London\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"Asia/Tokyo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Language\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-control\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"English\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"Spanish\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"French\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"German\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              children: \"Save Changes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 15\n          }, this), activeTab === 'security' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '1.5rem'\n              },\n              children: \"Security Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Two-Factor Authentication\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Enable 2FA for enhanced security\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Session Timeout (minutes)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                className: \"form-control\",\n                defaultValue: \"30\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Password Policy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    defaultChecked: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 121,\n                    columnNumber: 23\n                  }, this), \"Minimum 8 characters\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    defaultChecked: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 23\n                  }, this), \"Require uppercase letters\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    defaultChecked: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 23\n                  }, this), \"Require numbers\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              children: \"Save Changes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this), activeTab === 'notifications' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '1.5rem'\n              },\n              children: \"Notification Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Email Notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    defaultChecked: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 23\n                  }, this), \"New user registrations\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    defaultChecked: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 23\n                  }, this), \"System alerts\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 23\n                  }, this), \"Weekly reports\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Push Notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  defaultChecked: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Enable browser notifications\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              children: \"Save Changes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this), activeTab === 'appearance' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '1.5rem'\n              },\n              children: \"Appearance Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Theme\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-control\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"Light\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"Dark\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"Auto\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Sidebar Position\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-control\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"Left\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"Right\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Compact Mode\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Enable compact layout\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              children: \"Save Changes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_s(Settings, \"7PsXmoN53CpbLiBegwiEB5cr4Z8=\", false, function () {\n  return [useAuth];\n});\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "jsxDEV", "_jsxDEV", "Settings", "_s", "user", "activeTab", "setActiveTab", "tabs", "id", "label", "icon", "style", "padding", "children", "className", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "display", "gridTemplateColumns", "gap", "map", "tab", "onClick", "width", "border", "background", "borderRadius", "cursor", "alignItems", "textAlign", "transition", "type", "defaultValue", "disabled", "flexDirection", "defaultChecked", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Settings.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../context/AuthContext';\n\nconst Settings = () => {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState('general');\n\n  const tabs = [\n    { id: 'general', label: 'General', icon: '⚙️' },\n    { id: 'security', label: 'Security', icon: '🔒' },\n    { id: 'notifications', label: 'Notifications', icon: '🔔' },\n    { id: 'appearance', label: 'Appearance', icon: '🎨' },\n  ];\n\n  return (\n    <div style={{ padding: '2rem' }}>\n      {/* Header */}\n      <div className=\"card\" style={{ marginBottom: '2rem' }}>\n        <div className=\"card-header\">\n          <h1 className=\"card-title\">Settings</h1>\n        </div>\n        <div className=\"card-body\">\n          <p style={{ color: 'var(--text-secondary)' }}>\n            Manage your application settings and preferences.\n          </p>\n        </div>\n      </div>\n\n      <div style={{ display: 'grid', gridTemplateColumns: '250px 1fr', gap: '2rem' }}>\n        {/* Sidebar */}\n        <div className=\"card\">\n          <div className=\"card-body\" style={{ padding: '1rem' }}>\n            <nav>\n              {tabs.map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem 1rem',\n                    border: 'none',\n                    background: activeTab === tab.id ? 'var(--primary-color)' : 'transparent',\n                    color: activeTab === tab.id ? 'white' : 'var(--text-primary)',\n                    borderRadius: '8px',\n                    marginBottom: '0.5rem',\n                    cursor: 'pointer',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.75rem',\n                    textAlign: 'left',\n                    transition: 'all 0.2s ease',\n                  }}\n                >\n                  <span>{tab.icon}</span>\n                  {tab.label}\n                </button>\n              ))}\n            </nav>\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"card\">\n          <div className=\"card-body\">\n            {activeTab === 'general' && (\n              <div>\n                <h3 style={{ marginBottom: '1.5rem' }}>General Settings</h3>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Application Name</label>\n                  <input\n                    type=\"text\"\n                    className=\"form-control\"\n                    defaultValue=\"Admin Panel\"\n                    disabled\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Time Zone</label>\n                  <select className=\"form-control\">\n                    <option>UTC</option>\n                    <option>America/New_York</option>\n                    <option>Europe/London</option>\n                    <option>Asia/Tokyo</option>\n                  </select>\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Language</label>\n                  <select className=\"form-control\">\n                    <option>English</option>\n                    <option>Spanish</option>\n                    <option>French</option>\n                    <option>German</option>\n                  </select>\n                </div>\n                <button className=\"btn btn-primary\">Save Changes</button>\n              </div>\n            )}\n\n            {activeTab === 'security' && (\n              <div>\n                <h3 style={{ marginBottom: '1.5rem' }}>Security Settings</h3>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Two-Factor Authentication</label>\n                  <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n                    <input type=\"checkbox\" />\n                    <span>Enable 2FA for enhanced security</span>\n                  </div>\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Session Timeout (minutes)</label>\n                  <input\n                    type=\"number\"\n                    className=\"form-control\"\n                    defaultValue=\"30\"\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Password Policy</label>\n                  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n                    <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                      <input type=\"checkbox\" defaultChecked />\n                      Minimum 8 characters\n                    </label>\n                    <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                      <input type=\"checkbox\" defaultChecked />\n                      Require uppercase letters\n                    </label>\n                    <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                      <input type=\"checkbox\" defaultChecked />\n                      Require numbers\n                    </label>\n                  </div>\n                </div>\n                <button className=\"btn btn-primary\">Save Changes</button>\n              </div>\n            )}\n\n            {activeTab === 'notifications' && (\n              <div>\n                <h3 style={{ marginBottom: '1.5rem' }}>Notification Settings</h3>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Email Notifications</label>\n                  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n                    <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                      <input type=\"checkbox\" defaultChecked />\n                      New user registrations\n                    </label>\n                    <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                      <input type=\"checkbox\" defaultChecked />\n                      System alerts\n                    </label>\n                    <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                      <input type=\"checkbox\" />\n                      Weekly reports\n                    </label>\n                  </div>\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Push Notifications</label>\n                  <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n                    <input type=\"checkbox\" defaultChecked />\n                    <span>Enable browser notifications</span>\n                  </div>\n                </div>\n                <button className=\"btn btn-primary\">Save Changes</button>\n              </div>\n            )}\n\n            {activeTab === 'appearance' && (\n              <div>\n                <h3 style={{ marginBottom: '1.5rem' }}>Appearance Settings</h3>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Theme</label>\n                  <select className=\"form-control\">\n                    <option>Light</option>\n                    <option>Dark</option>\n                    <option>Auto</option>\n                  </select>\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Sidebar Position</label>\n                  <select className=\"form-control\">\n                    <option>Left</option>\n                    <option>Right</option>\n                  </select>\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Compact Mode</label>\n                  <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n                    <input type=\"checkbox\" />\n                    <span>Enable compact layout</span>\n                  </div>\n                </div>\n                <button className=\"btn btn-primary\">Save Changes</button>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAK,CAAC,GAAGL,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACM,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,SAAS,CAAC;EAErD,MAAMS,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC/C;IAAEF,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAK,CAAC,EACjD;IAAEF,EAAE,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC3D;IAAEF,EAAE,EAAE,YAAY;IAAEC,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAK,CAAC,CACtD;EAED,oBACET,OAAA;IAAKU,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAE9BZ,OAAA;MAAKa,SAAS,EAAC,MAAM;MAACH,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACpDZ,OAAA;QAAKa,SAAS,EAAC,aAAa;QAAAD,QAAA,eAC1BZ,OAAA;UAAIa,SAAS,EAAC,YAAY;UAAAD,QAAA,EAAC;QAAQ;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACNlB,OAAA;QAAKa,SAAS,EAAC,WAAW;QAAAD,QAAA,eACxBZ,OAAA;UAAGU,KAAK,EAAE;YAAES,KAAK,EAAE;UAAwB,CAAE;UAAAP,QAAA,EAAC;QAE9C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlB,OAAA;MAAKU,KAAK,EAAE;QAAEU,OAAO,EAAE,MAAM;QAAEC,mBAAmB,EAAE,WAAW;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAV,QAAA,gBAE7EZ,OAAA;QAAKa,SAAS,EAAC,MAAM;QAAAD,QAAA,eACnBZ,OAAA;UAAKa,SAAS,EAAC,WAAW;UAACH,KAAK,EAAE;YAAEC,OAAO,EAAE;UAAO,CAAE;UAAAC,QAAA,eACpDZ,OAAA;YAAAY,QAAA,EACGN,IAAI,CAACiB,GAAG,CAAEC,GAAG,iBACZxB,OAAA;cAEEyB,OAAO,EAAEA,CAAA,KAAMpB,YAAY,CAACmB,GAAG,CAACjB,EAAE,CAAE;cACpCG,KAAK,EAAE;gBACLgB,KAAK,EAAE,MAAM;gBACbf,OAAO,EAAE,cAAc;gBACvBgB,MAAM,EAAE,MAAM;gBACdC,UAAU,EAAExB,SAAS,KAAKoB,GAAG,CAACjB,EAAE,GAAG,sBAAsB,GAAG,aAAa;gBACzEY,KAAK,EAAEf,SAAS,KAAKoB,GAAG,CAACjB,EAAE,GAAG,OAAO,GAAG,qBAAqB;gBAC7DsB,YAAY,EAAE,KAAK;gBACnBf,YAAY,EAAE,QAAQ;gBACtBgB,MAAM,EAAE,SAAS;gBACjBV,OAAO,EAAE,MAAM;gBACfW,UAAU,EAAE,QAAQ;gBACpBT,GAAG,EAAE,SAAS;gBACdU,SAAS,EAAE,MAAM;gBACjBC,UAAU,EAAE;cACd,CAAE;cAAArB,QAAA,gBAEFZ,OAAA;gBAAAY,QAAA,EAAOY,GAAG,CAACf;cAAI;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACtBM,GAAG,CAAChB,KAAK;YAAA,GAnBLgB,GAAG,CAACjB,EAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBL,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlB,OAAA;QAAKa,SAAS,EAAC,MAAM;QAAAD,QAAA,eACnBZ,OAAA;UAAKa,SAAS,EAAC,WAAW;UAAAD,QAAA,GACvBR,SAAS,KAAK,SAAS,iBACtBJ,OAAA;YAAAY,QAAA,gBACEZ,OAAA;cAAIU,KAAK,EAAE;gBAAEI,YAAY,EAAE;cAAS,CAAE;cAAAF,QAAA,EAAC;YAAgB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DlB,OAAA;cAAKa,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBZ,OAAA;gBAAOa,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtDlB,OAAA;gBACEkC,IAAI,EAAC,MAAM;gBACXrB,SAAS,EAAC,cAAc;gBACxBsB,YAAY,EAAC,aAAa;gBAC1BC,QAAQ;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlB,OAAA;cAAKa,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBZ,OAAA;gBAAOa,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/ClB,OAAA;gBAAQa,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBAC9BZ,OAAA;kBAAAY,QAAA,EAAQ;gBAAG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpBlB,OAAA;kBAAAY,QAAA,EAAQ;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjClB,OAAA;kBAAAY,QAAA,EAAQ;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9BlB,OAAA;kBAAAY,QAAA,EAAQ;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNlB,OAAA;cAAKa,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBZ,OAAA;gBAAOa,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9ClB,OAAA;gBAAQa,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBAC9BZ,OAAA;kBAAAY,QAAA,EAAQ;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxBlB,OAAA;kBAAAY,QAAA,EAAQ;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxBlB,OAAA;kBAAAY,QAAA,EAAQ;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvBlB,OAAA;kBAAAY,QAAA,EAAQ;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNlB,OAAA;cAAQa,SAAS,EAAC,iBAAiB;cAAAD,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CACN,EAEAd,SAAS,KAAK,UAAU,iBACvBJ,OAAA;YAAAY,QAAA,gBACEZ,OAAA;cAAIU,KAAK,EAAE;gBAAEI,YAAY,EAAE;cAAS,CAAE;cAAAF,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7DlB,OAAA;cAAKa,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBZ,OAAA;gBAAOa,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAyB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/DlB,OAAA;gBAAKU,KAAK,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEW,UAAU,EAAE,QAAQ;kBAAET,GAAG,EAAE;gBAAO,CAAE;gBAAAV,QAAA,gBACjEZ,OAAA;kBAAOkC,IAAI,EAAC;gBAAU;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzBlB,OAAA;kBAAAY,QAAA,EAAM;gBAAgC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlB,OAAA;cAAKa,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBZ,OAAA;gBAAOa,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAyB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/DlB,OAAA;gBACEkC,IAAI,EAAC,QAAQ;gBACbrB,SAAS,EAAC,cAAc;gBACxBsB,YAAY,EAAC;cAAI;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlB,OAAA;cAAKa,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBZ,OAAA;gBAAOa,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrDlB,OAAA;gBAAKU,KAAK,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEiB,aAAa,EAAE,QAAQ;kBAAEf,GAAG,EAAE;gBAAS,CAAE;gBAAAV,QAAA,gBACtEZ,OAAA;kBAAOU,KAAK,EAAE;oBAAEU,OAAO,EAAE,MAAM;oBAAEW,UAAU,EAAE,QAAQ;oBAAET,GAAG,EAAE;kBAAS,CAAE;kBAAAV,QAAA,gBACrEZ,OAAA;oBAAOkC,IAAI,EAAC,UAAU;oBAACI,cAAc;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,wBAE1C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRlB,OAAA;kBAAOU,KAAK,EAAE;oBAAEU,OAAO,EAAE,MAAM;oBAAEW,UAAU,EAAE,QAAQ;oBAAET,GAAG,EAAE;kBAAS,CAAE;kBAAAV,QAAA,gBACrEZ,OAAA;oBAAOkC,IAAI,EAAC,UAAU;oBAACI,cAAc;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,6BAE1C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRlB,OAAA;kBAAOU,KAAK,EAAE;oBAAEU,OAAO,EAAE,MAAM;oBAAEW,UAAU,EAAE,QAAQ;oBAAET,GAAG,EAAE;kBAAS,CAAE;kBAAAV,QAAA,gBACrEZ,OAAA;oBAAOkC,IAAI,EAAC,UAAU;oBAACI,cAAc;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,mBAE1C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlB,OAAA;cAAQa,SAAS,EAAC,iBAAiB;cAAAD,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CACN,EAEAd,SAAS,KAAK,eAAe,iBAC5BJ,OAAA;YAAAY,QAAA,gBACEZ,OAAA;cAAIU,KAAK,EAAE;gBAAEI,YAAY,EAAE;cAAS,CAAE;cAAAF,QAAA,EAAC;YAAqB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjElB,OAAA;cAAKa,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBZ,OAAA;gBAAOa,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzDlB,OAAA;gBAAKU,KAAK,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEiB,aAAa,EAAE,QAAQ;kBAAEf,GAAG,EAAE;gBAAS,CAAE;gBAAAV,QAAA,gBACtEZ,OAAA;kBAAOU,KAAK,EAAE;oBAAEU,OAAO,EAAE,MAAM;oBAAEW,UAAU,EAAE,QAAQ;oBAAET,GAAG,EAAE;kBAAS,CAAE;kBAAAV,QAAA,gBACrEZ,OAAA;oBAAOkC,IAAI,EAAC,UAAU;oBAACI,cAAc;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,0BAE1C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRlB,OAAA;kBAAOU,KAAK,EAAE;oBAAEU,OAAO,EAAE,MAAM;oBAAEW,UAAU,EAAE,QAAQ;oBAAET,GAAG,EAAE;kBAAS,CAAE;kBAAAV,QAAA,gBACrEZ,OAAA;oBAAOkC,IAAI,EAAC,UAAU;oBAACI,cAAc;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,iBAE1C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRlB,OAAA;kBAAOU,KAAK,EAAE;oBAAEU,OAAO,EAAE,MAAM;oBAAEW,UAAU,EAAE,QAAQ;oBAAET,GAAG,EAAE;kBAAS,CAAE;kBAAAV,QAAA,gBACrEZ,OAAA;oBAAOkC,IAAI,EAAC;kBAAU;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,kBAE3B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlB,OAAA;cAAKa,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBZ,OAAA;gBAAOa,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxDlB,OAAA;gBAAKU,KAAK,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEW,UAAU,EAAE,QAAQ;kBAAET,GAAG,EAAE;gBAAO,CAAE;gBAAAV,QAAA,gBACjEZ,OAAA;kBAAOkC,IAAI,EAAC,UAAU;kBAACI,cAAc;gBAAA;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxClB,OAAA;kBAAAY,QAAA,EAAM;gBAA4B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlB,OAAA;cAAQa,SAAS,EAAC,iBAAiB;cAAAD,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CACN,EAEAd,SAAS,KAAK,YAAY,iBACzBJ,OAAA;YAAAY,QAAA,gBACEZ,OAAA;cAAIU,KAAK,EAAE;gBAAEI,YAAY,EAAE;cAAS,CAAE;cAAAF,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DlB,OAAA;cAAKa,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBZ,OAAA;gBAAOa,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3ClB,OAAA;gBAAQa,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBAC9BZ,OAAA;kBAAAY,QAAA,EAAQ;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtBlB,OAAA;kBAAAY,QAAA,EAAQ;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrBlB,OAAA;kBAAAY,QAAA,EAAQ;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNlB,OAAA;cAAKa,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBZ,OAAA;gBAAOa,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtDlB,OAAA;gBAAQa,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBAC9BZ,OAAA;kBAAAY,QAAA,EAAQ;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrBlB,OAAA;kBAAAY,QAAA,EAAQ;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNlB,OAAA;cAAKa,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBZ,OAAA;gBAAOa,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClDlB,OAAA;gBAAKU,KAAK,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEW,UAAU,EAAE,QAAQ;kBAAET,GAAG,EAAE;gBAAO,CAAE;gBAAAV,QAAA,gBACjEZ,OAAA;kBAAOkC,IAAI,EAAC;gBAAU;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzBlB,OAAA;kBAAAY,QAAA,EAAM;gBAAqB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlB,OAAA;cAAQa,SAAS,EAAC,iBAAiB;cAAAD,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CAtMID,QAAQ;EAAA,QACKH,OAAO;AAAA;AAAAyC,EAAA,GADpBtC,QAAQ;AAwMd,eAAeA,QAAQ;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}