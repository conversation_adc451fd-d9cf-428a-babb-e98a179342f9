{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/UserManagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { usersAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserManagement = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedRole, setSelectedRole] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [showAddModal, setShowAddModal] = useState(false);\n  useEffect(() => {\n    loadUsers();\n  }, [currentPage, searchTerm, selectedRole]);\n  const loadUsers = async () => {\n    try {\n      setLoading(true);\n      const response = await usersAPI.getUsers({\n        page: currentPage,\n        limit: 10,\n        search: searchTerm,\n        role: selectedRole\n      });\n      if (response.success) {\n        setUsers(response.data);\n        setTotalPages(response.pagination.pages);\n      }\n    } catch (error) {\n      console.error('Error loading users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSearch = e => {\n    setSearchTerm(e.target.value);\n    setCurrentPage(1);\n  };\n  const handleRoleFilter = e => {\n    setSelectedRole(e.target.value);\n    setCurrentPage(1);\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n  const getRoleColor = role => {\n    switch (role) {\n      case 'superadmin':\n        return '#ef4444';\n      case 'admin':\n        return '#3b82f6';\n      case 'agent':\n        return '#10b981';\n      default:\n        return '#64748b';\n    }\n  };\n  const handleToggleStatus = async userId => {\n    try {\n      await usersAPI.toggleUserStatus(userId);\n      loadUsers(); // Reload users after status change\n    } catch (error) {\n      console.error('Error toggling user status:', error);\n    }\n  };\n  const handleDeleteUser = async userId => {\n    if (window.confirm('Are you sure you want to delete this user?')) {\n      try {\n        await usersAPI.deleteUser(userId);\n        loadUsers(); // Reload users after deletion\n      } catch (error) {\n        console.error('Error deleting user:', error);\n      }\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '200px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Loading users...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      style: {\n        marginBottom: '2rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"card-title\",\n          children: \"User Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: () => setShowAddModal(true),\n          children: \"Add User\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      style: {\n        marginBottom: '2rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            style: {\n              marginBottom: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-control\",\n              placeholder: \"Search users...\",\n              value: searchTerm,\n              onChange: handleSearch\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            style: {\n              marginBottom: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-control\",\n              value: selectedRole,\n              onChange: handleRoleFilter,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All Roles\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"admin\",\n                children: \"Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"agent\",\n                children: \"Agent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this), (user === null || user === void 0 ? void 0 : user.role) === 'superadmin' && /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"superadmin\",\n                children: \"Superadmin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 49\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: [users.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            overflowX: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            style: {\n              width: '100%',\n              borderCollapse: 'collapse'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                style: {\n                  borderBottom: '2px solid var(--border-color)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: '1rem',\n                    textAlign: 'left',\n                    color: 'var(--text-secondary)',\n                    fontWeight: '600'\n                  },\n                  children: \"User\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: '1rem',\n                    textAlign: 'left',\n                    color: 'var(--text-secondary)',\n                    fontWeight: '600'\n                  },\n                  children: \"Role\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: '1rem',\n                    textAlign: 'left',\n                    color: 'var(--text-secondary)',\n                    fontWeight: '600'\n                  },\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: '1rem',\n                    textAlign: 'left',\n                    color: 'var(--text-secondary)',\n                    fontWeight: '600'\n                  },\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: '1rem',\n                    textAlign: 'left',\n                    color: 'var(--text-secondary)',\n                    fontWeight: '600'\n                  },\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: users.map(userItem => /*#__PURE__*/_jsxDEV(\"tr\", {\n                style: {\n                  borderBottom: '1px solid var(--border-color)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '1rem'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontWeight: '500',\n                        color: 'var(--text-primary)'\n                      },\n                      children: userItem.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.875rem',\n                        color: 'var(--text-secondary)'\n                      },\n                      children: userItem.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.75rem',\n                        color: 'var(--text-secondary)'\n                      },\n                      children: [\"@\", userItem.username]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '1rem'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '9999px',\n                      fontSize: '0.75rem',\n                      fontWeight: '500',\n                      backgroundColor: getRoleColor(userItem.role) + '20',\n                      color: getRoleColor(userItem.role)\n                    },\n                    children: userItem.role\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '1rem'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '9999px',\n                      fontSize: '0.75rem',\n                      fontWeight: '500',\n                      backgroundColor: userItem.isActive ? 'var(--success-color)20' : 'var(--error-color)20',\n                      color: userItem.isActive ? 'var(--success-color)' : 'var(--error-color)'\n                    },\n                    children: userItem.isActive ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '1rem',\n                    color: 'var(--text-secondary)'\n                  },\n                  children: formatDate(userItem.createdAt)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '1rem'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      gap: '0.5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-sm btn-secondary\",\n                      onClick: () => handleToggleStatus(userItem._id),\n                      disabled: userItem.role === 'superadmin',\n                      children: userItem.isActive ? 'Deactivate' : 'Activate'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 27\n                    }, this), (user === null || user === void 0 ? void 0 : user.role) === 'superadmin' && userItem.role !== 'superadmin' && /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-sm btn-danger\",\n                      onClick: () => handleDeleteUser(userItem._id),\n                      children: \"Delete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 227,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 23\n                }, this)]\n              }, userItem._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '2rem',\n            color: 'var(--text-secondary)'\n          },\n          children: \"No users found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'center',\n            marginTop: '2rem',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary btn-sm\",\n            onClick: () => setCurrentPage(prev => Math.max(prev - 1, 1)),\n            disabled: currentPage === 1,\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              padding: '0.5rem 1rem',\n              color: 'var(--text-secondary)'\n            },\n            children: [\"Page \", currentPage, \" of \", totalPages]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary btn-sm\",\n            onClick: () => setCurrentPage(prev => Math.min(prev + 1, totalPages)),\n            disabled: currentPage === totalPages,\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), showAddModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: 'white',\n          padding: '2rem',\n          borderRadius: '12px',\n          maxWidth: '500px',\n          width: '90%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: \"Add New User\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: 'var(--text-secondary)',\n            marginBottom: '2rem'\n          },\n          children: \"Add user functionality will be implemented here.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            justifyContent: 'flex-end'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: () => setShowAddModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: () => setShowAddModal(false),\n            children: \"Add User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s(UserManagement, \"f6DEKQmJV0IK0KC/sYuDBQtcTEU=\", false, function () {\n  return [useAuth];\n});\n_c = UserManagement;\nexport default UserManagement;\nvar _c;\n$RefreshReg$(_c, \"UserManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "usersAPI", "jsxDEV", "_jsxDEV", "UserManagement", "_s", "user", "users", "setUsers", "loading", "setLoading", "searchTerm", "setSearchTerm", "selectedR<PERSON>", "setSelectedRole", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "showAddModal", "setShowAddModal", "loadUsers", "response", "getUsers", "page", "limit", "search", "role", "success", "data", "pagination", "pages", "error", "console", "handleSearch", "e", "target", "value", "handleRoleFilter", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "getRoleColor", "handleToggleStatus", "userId", "toggleUserStatus", "handleDeleteUser", "window", "confirm", "deleteUser", "style", "padding", "children", "display", "justifyContent", "alignItems", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "marginBottom", "onClick", "gridTemplateColumns", "gap", "type", "placeholder", "onChange", "length", "overflowX", "width", "borderCollapse", "borderBottom", "textAlign", "color", "fontWeight", "map", "userItem", "name", "fontSize", "email", "username", "borderRadius", "backgroundColor", "isActive", "createdAt", "_id", "disabled", "marginTop", "prev", "Math", "max", "min", "position", "top", "left", "right", "bottom", "zIndex", "max<PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/UserManagement.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { usersAPI } from '../services/api';\n\nconst UserManagement = () => {\n  const { user } = useAuth();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedRole, setSelectedRole] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [showAddModal, setShowAddModal] = useState(false);\n\n  useEffect(() => {\n    loadUsers();\n  }, [currentPage, searchTerm, selectedRole]);\n\n  const loadUsers = async () => {\n    try {\n      setLoading(true);\n      const response = await usersAPI.getUsers({\n        page: currentPage,\n        limit: 10,\n        search: searchTerm,\n        role: selectedRole,\n      });\n\n      if (response.success) {\n        setUsers(response.data);\n        setTotalPages(response.pagination.pages);\n      }\n    } catch (error) {\n      console.error('Error loading users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = (e) => {\n    setSearchTerm(e.target.value);\n    setCurrentPage(1);\n  };\n\n  const handleRoleFilter = (e) => {\n    setSelectedRole(e.target.value);\n    setCurrentPage(1);\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    });\n  };\n\n  const getRoleColor = (role) => {\n    switch (role) {\n      case 'superadmin':\n        return '#ef4444';\n      case 'admin':\n        return '#3b82f6';\n      case 'agent':\n        return '#10b981';\n      default:\n        return '#64748b';\n    }\n  };\n\n  const handleToggleStatus = async (userId) => {\n    try {\n      await usersAPI.toggleUserStatus(userId);\n      loadUsers(); // Reload users after status change\n    } catch (error) {\n      console.error('Error toggling user status:', error);\n    }\n  };\n\n  const handleDeleteUser = async (userId) => {\n    if (window.confirm('Are you sure you want to delete this user?')) {\n      try {\n        await usersAPI.deleteUser(userId);\n        loadUsers(); // Reload users after deletion\n      } catch (error) {\n        console.error('Error deleting user:', error);\n      }\n    }\n  };\n\n  if (loading) {\n    return (\n      <div style={{ padding: '2rem' }}>\n        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>\n          <div>Loading users...</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ padding: '2rem' }}>\n      {/* Header */}\n      <div className=\"card\" style={{ marginBottom: '2rem' }}>\n        <div className=\"card-header\">\n          <h1 className=\"card-title\">User Management</h1>\n          <button \n            className=\"btn btn-primary\"\n            onClick={() => setShowAddModal(true)}\n          >\n            Add User\n          </button>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"card\" style={{ marginBottom: '2rem' }}>\n        <div className=\"card-body\">\n          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>\n            <div className=\"form-group\" style={{ marginBottom: 0 }}>\n              <input\n                type=\"text\"\n                className=\"form-control\"\n                placeholder=\"Search users...\"\n                value={searchTerm}\n                onChange={handleSearch}\n              />\n            </div>\n            <div className=\"form-group\" style={{ marginBottom: 0 }}>\n              <select\n                className=\"form-control\"\n                value={selectedRole}\n                onChange={handleRoleFilter}\n              >\n                <option value=\"\">All Roles</option>\n                <option value=\"admin\">Admin</option>\n                <option value=\"agent\">Agent</option>\n                {user?.role === 'superadmin' && <option value=\"superadmin\">Superadmin</option>}\n              </select>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Users Table */}\n      <div className=\"card\">\n        <div className=\"card-body\">\n          {users.length > 0 ? (\n            <div style={{ overflowX: 'auto' }}>\n              <table style={{ width: '100%', borderCollapse: 'collapse' }}>\n                <thead>\n                  <tr style={{ borderBottom: '2px solid var(--border-color)' }}>\n                    <th style={{ padding: '1rem', textAlign: 'left', color: 'var(--text-secondary)', fontWeight: '600' }}>\n                      User\n                    </th>\n                    <th style={{ padding: '1rem', textAlign: 'left', color: 'var(--text-secondary)', fontWeight: '600' }}>\n                      Role\n                    </th>\n                    <th style={{ padding: '1rem', textAlign: 'left', color: 'var(--text-secondary)', fontWeight: '600' }}>\n                      Status\n                    </th>\n                    <th style={{ padding: '1rem', textAlign: 'left', color: 'var(--text-secondary)', fontWeight: '600' }}>\n                      Created\n                    </th>\n                    <th style={{ padding: '1rem', textAlign: 'left', color: 'var(--text-secondary)', fontWeight: '600' }}>\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {users.map((userItem) => (\n                    <tr key={userItem._id} style={{ borderBottom: '1px solid var(--border-color)' }}>\n                      <td style={{ padding: '1rem' }}>\n                        <div>\n                          <div style={{ fontWeight: '500', color: 'var(--text-primary)' }}>\n                            {userItem.name}\n                          </div>\n                          <div style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>\n                            {userItem.email}\n                          </div>\n                          <div style={{ fontSize: '0.75rem', color: 'var(--text-secondary)' }}>\n                            @{userItem.username}\n                          </div>\n                        </div>\n                      </td>\n                      <td style={{ padding: '1rem' }}>\n                        <span\n                          style={{\n                            padding: '0.25rem 0.75rem',\n                            borderRadius: '9999px',\n                            fontSize: '0.75rem',\n                            fontWeight: '500',\n                            backgroundColor: getRoleColor(userItem.role) + '20',\n                            color: getRoleColor(userItem.role),\n                          }}\n                        >\n                          {userItem.role}\n                        </span>\n                      </td>\n                      <td style={{ padding: '1rem' }}>\n                        <span\n                          style={{\n                            padding: '0.25rem 0.75rem',\n                            borderRadius: '9999px',\n                            fontSize: '0.75rem',\n                            fontWeight: '500',\n                            backgroundColor: userItem.isActive ? 'var(--success-color)20' : 'var(--error-color)20',\n                            color: userItem.isActive ? 'var(--success-color)' : 'var(--error-color)',\n                          }}\n                        >\n                          {userItem.isActive ? 'Active' : 'Inactive'}\n                        </span>\n                      </td>\n                      <td style={{ padding: '1rem', color: 'var(--text-secondary)' }}>\n                        {formatDate(userItem.createdAt)}\n                      </td>\n                      <td style={{ padding: '1rem' }}>\n                        <div style={{ display: 'flex', gap: '0.5rem' }}>\n                          <button\n                            className=\"btn btn-sm btn-secondary\"\n                            onClick={() => handleToggleStatus(userItem._id)}\n                            disabled={userItem.role === 'superadmin'}\n                          >\n                            {userItem.isActive ? 'Deactivate' : 'Activate'}\n                          </button>\n                          {user?.role === 'superadmin' && userItem.role !== 'superadmin' && (\n                            <button\n                              className=\"btn btn-sm btn-danger\"\n                              onClick={() => handleDeleteUser(userItem._id)}\n                            >\n                              Delete\n                            </button>\n                          )}\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          ) : (\n            <div style={{ textAlign: 'center', padding: '2rem', color: 'var(--text-secondary)' }}>\n              No users found\n            </div>\n          )}\n\n          {/* Pagination */}\n          {totalPages > 1 && (\n            <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem', gap: '0.5rem' }}>\n              <button\n                className=\"btn btn-secondary btn-sm\"\n                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}\n                disabled={currentPage === 1}\n              >\n                Previous\n              </button>\n              <span style={{ padding: '0.5rem 1rem', color: 'var(--text-secondary)' }}>\n                Page {currentPage} of {totalPages}\n              </span>\n              <button\n                className=\"btn btn-secondary btn-sm\"\n                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}\n                disabled={currentPage === totalPages}\n              >\n                Next\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Add User Modal Placeholder */}\n      {showAddModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n        }}>\n          <div style={{\n            backgroundColor: 'white',\n            padding: '2rem',\n            borderRadius: '12px',\n            maxWidth: '500px',\n            width: '90%',\n          }}>\n            <h3 style={{ marginBottom: '1rem' }}>Add New User</h3>\n            <p style={{ color: 'var(--text-secondary)', marginBottom: '2rem' }}>\n              Add user functionality will be implemented here.\n            </p>\n            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end' }}>\n              <button\n                className=\"btn btn-secondary\"\n                onClick={() => setShowAddModal(false)}\n              >\n                Cancel\n              </button>\n              <button\n                className=\"btn btn-primary\"\n                onClick={() => setShowAddModal(false)}\n              >\n                Add User\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default UserManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC;EAAK,CAAC,GAAGN,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACdsB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACN,WAAW,EAAEJ,UAAU,EAAEE,YAAY,CAAC,CAAC;EAE3C,MAAMQ,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMY,QAAQ,GAAG,MAAMrB,QAAQ,CAACsB,QAAQ,CAAC;QACvCC,IAAI,EAAET,WAAW;QACjBU,KAAK,EAAE,EAAE;QACTC,MAAM,EAAEf,UAAU;QAClBgB,IAAI,EAAEd;MACR,CAAC,CAAC;MAEF,IAAIS,QAAQ,CAACM,OAAO,EAAE;QACpBpB,QAAQ,CAACc,QAAQ,CAACO,IAAI,CAAC;QACvBX,aAAa,CAACI,QAAQ,CAACQ,UAAU,CAACC,KAAK,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,YAAY,GAAIC,CAAC,IAAK;IAC1BvB,aAAa,CAACuB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;IAC7BrB,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,MAAMsB,gBAAgB,GAAIH,CAAC,IAAK;IAC9BrB,eAAe,CAACqB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;IAC/BrB,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,MAAMuB,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAInB,IAAI,IAAK;IAC7B,QAAQA,IAAI;MACV,KAAK,YAAY;QACf,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMoB,kBAAkB,GAAG,MAAOC,MAAM,IAAK;IAC3C,IAAI;MACF,MAAM/C,QAAQ,CAACgD,gBAAgB,CAACD,MAAM,CAAC;MACvC3B,SAAS,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EAED,MAAMkB,gBAAgB,GAAG,MAAOF,MAAM,IAAK;IACzC,IAAIG,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;MAChE,IAAI;QACF,MAAMnD,QAAQ,CAACoD,UAAU,CAACL,MAAM,CAAC;QACjC3B,SAAS,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,OAAOW,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;IACF;EACF,CAAC;EAED,IAAIvB,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKmD,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,eAC9BrD,OAAA;QAAKmD,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,UAAU,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAQ,CAAE;QAAAJ,QAAA,eAC/FrD,OAAA;UAAAqD,QAAA,EAAK;QAAgB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7D,OAAA;IAAKmD,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAE9BrD,OAAA;MAAK8D,SAAS,EAAC,MAAM;MAACX,KAAK,EAAE;QAAEY,YAAY,EAAE;MAAO,CAAE;MAAAV,QAAA,eACpDrD,OAAA;QAAK8D,SAAS,EAAC,aAAa;QAAAT,QAAA,gBAC1BrD,OAAA;UAAI8D,SAAS,EAAC,YAAY;UAAAT,QAAA,EAAC;QAAe;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/C7D,OAAA;UACE8D,SAAS,EAAC,iBAAiB;UAC3BE,OAAO,EAAEA,CAAA,KAAM/C,eAAe,CAAC,IAAI,CAAE;UAAAoC,QAAA,EACtC;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA;MAAK8D,SAAS,EAAC,MAAM;MAACX,KAAK,EAAE;QAAEY,YAAY,EAAE;MAAO,CAAE;MAAAV,QAAA,eACpDrD,OAAA;QAAK8D,SAAS,EAAC,WAAW;QAAAT,QAAA,eACxBrD,OAAA;UAAKmD,KAAK,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEW,mBAAmB,EAAE,sCAAsC;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAAb,QAAA,gBACxGrD,OAAA;YAAK8D,SAAS,EAAC,YAAY;YAACX,KAAK,EAAE;cAAEY,YAAY,EAAE;YAAE,CAAE;YAAAV,QAAA,eACrDrD,OAAA;cACEmE,IAAI,EAAC,MAAM;cACXL,SAAS,EAAC,cAAc;cACxBM,WAAW,EAAC,iBAAiB;cAC7BlC,KAAK,EAAE1B,UAAW;cAClB6D,QAAQ,EAAEtC;YAAa;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN7D,OAAA;YAAK8D,SAAS,EAAC,YAAY;YAACX,KAAK,EAAE;cAAEY,YAAY,EAAE;YAAE,CAAE;YAAAV,QAAA,eACrDrD,OAAA;cACE8D,SAAS,EAAC,cAAc;cACxB5B,KAAK,EAAExB,YAAa;cACpB2D,QAAQ,EAAElC,gBAAiB;cAAAkB,QAAA,gBAE3BrD,OAAA;gBAAQkC,KAAK,EAAC,EAAE;gBAAAmB,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC7D,OAAA;gBAAQkC,KAAK,EAAC,OAAO;gBAAAmB,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC7D,OAAA;gBAAQkC,KAAK,EAAC,OAAO;gBAAAmB,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACnC,CAAA1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,IAAI,MAAK,YAAY,iBAAIxB,OAAA;gBAAQkC,KAAK,EAAC,YAAY;gBAAAmB,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA;MAAK8D,SAAS,EAAC,MAAM;MAAAT,QAAA,eACnBrD,OAAA;QAAK8D,SAAS,EAAC,WAAW;QAAAT,QAAA,GACvBjD,KAAK,CAACkE,MAAM,GAAG,CAAC,gBACftE,OAAA;UAAKmD,KAAK,EAAE;YAAEoB,SAAS,EAAE;UAAO,CAAE;UAAAlB,QAAA,eAChCrD,OAAA;YAAOmD,KAAK,EAAE;cAAEqB,KAAK,EAAE,MAAM;cAAEC,cAAc,EAAE;YAAW,CAAE;YAAApB,QAAA,gBAC1DrD,OAAA;cAAAqD,QAAA,eACErD,OAAA;gBAAImD,KAAK,EAAE;kBAAEuB,YAAY,EAAE;gBAAgC,CAAE;gBAAArB,QAAA,gBAC3DrD,OAAA;kBAAImD,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEuB,SAAS,EAAE,MAAM;oBAAEC,KAAK,EAAE,uBAAuB;oBAAEC,UAAU,EAAE;kBAAM,CAAE;kBAAAxB,QAAA,EAAC;gBAEtG;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL7D,OAAA;kBAAImD,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEuB,SAAS,EAAE,MAAM;oBAAEC,KAAK,EAAE,uBAAuB;oBAAEC,UAAU,EAAE;kBAAM,CAAE;kBAAAxB,QAAA,EAAC;gBAEtG;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL7D,OAAA;kBAAImD,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEuB,SAAS,EAAE,MAAM;oBAAEC,KAAK,EAAE,uBAAuB;oBAAEC,UAAU,EAAE;kBAAM,CAAE;kBAAAxB,QAAA,EAAC;gBAEtG;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL7D,OAAA;kBAAImD,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEuB,SAAS,EAAE,MAAM;oBAAEC,KAAK,EAAE,uBAAuB;oBAAEC,UAAU,EAAE;kBAAM,CAAE;kBAAAxB,QAAA,EAAC;gBAEtG;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL7D,OAAA;kBAAImD,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEuB,SAAS,EAAE,MAAM;oBAAEC,KAAK,EAAE,uBAAuB;oBAAEC,UAAU,EAAE;kBAAM,CAAE;kBAAAxB,QAAA,EAAC;gBAEtG;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR7D,OAAA;cAAAqD,QAAA,EACGjD,KAAK,CAAC0E,GAAG,CAAEC,QAAQ,iBAClB/E,OAAA;gBAAuBmD,KAAK,EAAE;kBAAEuB,YAAY,EAAE;gBAAgC,CAAE;gBAAArB,QAAA,gBAC9ErD,OAAA;kBAAImD,KAAK,EAAE;oBAAEC,OAAO,EAAE;kBAAO,CAAE;kBAAAC,QAAA,eAC7BrD,OAAA;oBAAAqD,QAAA,gBACErD,OAAA;sBAAKmD,KAAK,EAAE;wBAAE0B,UAAU,EAAE,KAAK;wBAAED,KAAK,EAAE;sBAAsB,CAAE;sBAAAvB,QAAA,EAC7D0B,QAAQ,CAACC;oBAAI;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACN7D,OAAA;sBAAKmD,KAAK,EAAE;wBAAE8B,QAAQ,EAAE,UAAU;wBAAEL,KAAK,EAAE;sBAAwB,CAAE;sBAAAvB,QAAA,EAClE0B,QAAQ,CAACG;oBAAK;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC,eACN7D,OAAA;sBAAKmD,KAAK,EAAE;wBAAE8B,QAAQ,EAAE,SAAS;wBAAEL,KAAK,EAAE;sBAAwB,CAAE;sBAAAvB,QAAA,GAAC,GAClE,EAAC0B,QAAQ,CAACI,QAAQ;oBAAA;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACL7D,OAAA;kBAAImD,KAAK,EAAE;oBAAEC,OAAO,EAAE;kBAAO,CAAE;kBAAAC,QAAA,eAC7BrD,OAAA;oBACEmD,KAAK,EAAE;sBACLC,OAAO,EAAE,iBAAiB;sBAC1BgC,YAAY,EAAE,QAAQ;sBACtBH,QAAQ,EAAE,SAAS;sBACnBJ,UAAU,EAAE,KAAK;sBACjBQ,eAAe,EAAE1C,YAAY,CAACoC,QAAQ,CAACvD,IAAI,CAAC,GAAG,IAAI;sBACnDoD,KAAK,EAAEjC,YAAY,CAACoC,QAAQ,CAACvD,IAAI;oBACnC,CAAE;oBAAA6B,QAAA,EAED0B,QAAQ,CAACvD;kBAAI;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACL7D,OAAA;kBAAImD,KAAK,EAAE;oBAAEC,OAAO,EAAE;kBAAO,CAAE;kBAAAC,QAAA,eAC7BrD,OAAA;oBACEmD,KAAK,EAAE;sBACLC,OAAO,EAAE,iBAAiB;sBAC1BgC,YAAY,EAAE,QAAQ;sBACtBH,QAAQ,EAAE,SAAS;sBACnBJ,UAAU,EAAE,KAAK;sBACjBQ,eAAe,EAAEN,QAAQ,CAACO,QAAQ,GAAG,wBAAwB,GAAG,sBAAsB;sBACtFV,KAAK,EAAEG,QAAQ,CAACO,QAAQ,GAAG,sBAAsB,GAAG;oBACtD,CAAE;oBAAAjC,QAAA,EAED0B,QAAQ,CAACO,QAAQ,GAAG,QAAQ,GAAG;kBAAU;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACL7D,OAAA;kBAAImD,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEwB,KAAK,EAAE;kBAAwB,CAAE;kBAAAvB,QAAA,EAC5DjB,UAAU,CAAC2C,QAAQ,CAACQ,SAAS;gBAAC;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,eACL7D,OAAA;kBAAImD,KAAK,EAAE;oBAAEC,OAAO,EAAE;kBAAO,CAAE;kBAAAC,QAAA,eAC7BrD,OAAA;oBAAKmD,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEY,GAAG,EAAE;oBAAS,CAAE;oBAAAb,QAAA,gBAC7CrD,OAAA;sBACE8D,SAAS,EAAC,0BAA0B;sBACpCE,OAAO,EAAEA,CAAA,KAAMpB,kBAAkB,CAACmC,QAAQ,CAACS,GAAG,CAAE;sBAChDC,QAAQ,EAAEV,QAAQ,CAACvD,IAAI,KAAK,YAAa;sBAAA6B,QAAA,EAExC0B,QAAQ,CAACO,QAAQ,GAAG,YAAY,GAAG;oBAAU;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC,EACR,CAAA1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,IAAI,MAAK,YAAY,IAAIuD,QAAQ,CAACvD,IAAI,KAAK,YAAY,iBAC5DxB,OAAA;sBACE8D,SAAS,EAAC,uBAAuB;sBACjCE,OAAO,EAAEA,CAAA,KAAMjB,gBAAgB,CAACgC,QAAQ,CAACS,GAAG,CAAE;sBAAAnC,QAAA,EAC/C;oBAED;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GA/DEkB,QAAQ,CAACS,GAAG;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgEjB,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,gBAEN7D,OAAA;UAAKmD,KAAK,EAAE;YAAEwB,SAAS,EAAE,QAAQ;YAAEvB,OAAO,EAAE,MAAM;YAAEwB,KAAK,EAAE;UAAwB,CAAE;UAAAvB,QAAA,EAAC;QAEtF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,EAGA/C,UAAU,GAAG,CAAC,iBACbd,OAAA;UAAKmD,KAAK,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEmC,SAAS,EAAE,MAAM;YAAExB,GAAG,EAAE;UAAS,CAAE;UAAAb,QAAA,gBAC1FrD,OAAA;YACE8D,SAAS,EAAC,0BAA0B;YACpCE,OAAO,EAAEA,CAAA,KAAMnD,cAAc,CAAC8E,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAE;YAC7DF,QAAQ,EAAE7E,WAAW,KAAK,CAAE;YAAAyC,QAAA,EAC7B;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7D,OAAA;YAAMmD,KAAK,EAAE;cAAEC,OAAO,EAAE,aAAa;cAAEwB,KAAK,EAAE;YAAwB,CAAE;YAAAvB,QAAA,GAAC,OAClE,EAACzC,WAAW,EAAC,MAAI,EAACE,UAAU;UAAA;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACP7D,OAAA;YACE8D,SAAS,EAAC,0BAA0B;YACpCE,OAAO,EAAEA,CAAA,KAAMnD,cAAc,CAAC8E,IAAI,IAAIC,IAAI,CAACE,GAAG,CAACH,IAAI,GAAG,CAAC,EAAE7E,UAAU,CAAC,CAAE;YACtE2E,QAAQ,EAAE7E,WAAW,KAAKE,UAAW;YAAAuC,QAAA,EACtC;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL7C,YAAY,iBACXhB,OAAA;MAAKmD,KAAK,EAAE;QACV4C,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTd,eAAe,EAAE,oBAAoB;QACrC/B,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxB6C,MAAM,EAAE;MACV,CAAE;MAAA/C,QAAA,eACArD,OAAA;QAAKmD,KAAK,EAAE;UACVkC,eAAe,EAAE,OAAO;UACxBjC,OAAO,EAAE,MAAM;UACfgC,YAAY,EAAE,MAAM;UACpBiB,QAAQ,EAAE,OAAO;UACjB7B,KAAK,EAAE;QACT,CAAE;QAAAnB,QAAA,gBACArD,OAAA;UAAImD,KAAK,EAAE;YAAEY,YAAY,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAY;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtD7D,OAAA;UAAGmD,KAAK,EAAE;YAAEyB,KAAK,EAAE,uBAAuB;YAAEb,YAAY,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAEpE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ7D,OAAA;UAAKmD,KAAK,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEY,GAAG,EAAE,MAAM;YAAEX,cAAc,EAAE;UAAW,CAAE;UAAAF,QAAA,gBACvErD,OAAA;YACE8D,SAAS,EAAC,mBAAmB;YAC7BE,OAAO,EAAEA,CAAA,KAAM/C,eAAe,CAAC,KAAK,CAAE;YAAAoC,QAAA,EACvC;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7D,OAAA;YACE8D,SAAS,EAAC,iBAAiB;YAC3BE,OAAO,EAAEA,CAAA,KAAM/C,eAAe,CAAC,KAAK,CAAE;YAAAoC,QAAA,EACvC;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC3D,EAAA,CAvTID,cAAc;EAAA,QACDJ,OAAO;AAAA;AAAAyG,EAAA,GADpBrG,cAAc;AAyTpB,eAAeA,cAAc;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}