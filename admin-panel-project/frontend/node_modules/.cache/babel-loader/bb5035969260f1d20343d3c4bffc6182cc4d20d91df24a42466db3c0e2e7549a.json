{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/context/AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { authAPI } from '../services/api';\n\n// Initial state\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst initialState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  LOGOUT: 'LOGOUT',\n  LOAD_USER_START: 'LOAD_USER_START',\n  LOAD_USER_SUCCESS: 'LOAD_USER_SUCCESS',\n  LOAD_USER_FAILURE: 'LOAD_USER_FAILURE',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n  UPDATE_USER: 'UPDATE_USER'\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n      return {\n        ...state,\n        isLoading: true,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload\n      };\n    case AUTH_ACTIONS.LOGOUT:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOAD_USER_START:\n      return {\n        ...state,\n        isLoading: true\n      };\n    case AUTH_ACTIONS.LOAD_USER_SUCCESS:\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOAD_USER_FAILURE:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload\n      };\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n    case AUTH_ACTIONS.UPDATE_USER:\n      return {\n        ...state,\n        user: {\n          ...state.user,\n          ...action.payload\n        }\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = /*#__PURE__*/createContext();\n\n// Auth provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Load user on app start\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    const user = localStorage.getItem('user');\n    if (token && user) {\n      try {\n        const parsedUser = JSON.parse(user);\n        dispatch({\n          type: AUTH_ACTIONS.LOGIN_SUCCESS,\n          payload: {\n            user: parsedUser,\n            token\n          }\n        });\n\n        // Verify token is still valid\n        loadUser();\n      } catch (error) {\n        console.error('Error parsing stored user:', error);\n        logout();\n      }\n    } else {\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_FAILURE,\n        payload: null\n      });\n    }\n  }, []);\n\n  // Login function\n  const login = async credentials => {\n    try {\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_START\n      });\n      const response = await authAPI.login(credentials);\n      if (response.success) {\n        const {\n          user,\n          token\n        } = response;\n\n        // Store in localStorage\n        localStorage.setItem('token', token);\n        localStorage.setItem('user', JSON.stringify(user));\n        dispatch({\n          type: AUTH_ACTIONS.LOGIN_SUCCESS,\n          payload: {\n            user,\n            token\n          }\n        });\n        return {\n          success: true\n        };\n      } else {\n        throw new Error(response.message || 'Login failed');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Logout function\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    dispatch({\n      type: AUTH_ACTIONS.LOGOUT\n    });\n  };\n\n  // Load current user\n  const loadUser = async () => {\n    try {\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_START\n      });\n      const response = await authAPI.getCurrentUser();\n      if (response.success) {\n        dispatch({\n          type: AUTH_ACTIONS.LOAD_USER_SUCCESS,\n          payload: response.user\n        });\n\n        // Update localStorage\n        localStorage.setItem('user', JSON.stringify(response.user));\n      } else {\n        throw new Error(response.message || 'Failed to load user');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Load user error:', error);\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_FAILURE,\n        payload: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Failed to load user'\n      });\n\n      // Clear invalid token\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n    }\n  };\n\n  // Update user data\n  const updateUser = userData => {\n    dispatch({\n      type: AUTH_ACTIONS.UPDATE_USER,\n      payload: userData\n    });\n\n    // Update localStorage\n    const updatedUser = {\n      ...state.user,\n      ...userData\n    };\n    localStorage.setItem('user', JSON.stringify(updatedUser));\n  };\n\n  // Clear error\n  const clearError = () => {\n    dispatch({\n      type: AUTH_ACTIONS.CLEAR_ERROR\n    });\n  };\n\n  // Context value\n  const value = {\n    ...state,\n    login,\n    logout,\n    loadUser,\n    updateUser,\n    clearError\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 238,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"bgCdjuTOmPdSBRwTap80EFd9Y3U=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "authAPI", "jsxDEV", "_jsxDEV", "initialState", "user", "token", "isAuthenticated", "isLoading", "error", "AUTH_ACTIONS", "LOGIN_START", "LOGIN_SUCCESS", "LOGIN_FAILURE", "LOGOUT", "LOAD_USER_START", "LOAD_USER_SUCCESS", "LOAD_USER_FAILURE", "CLEAR_ERROR", "UPDATE_USER", "authReducer", "state", "action", "type", "payload", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "localStorage", "getItem", "parsedUser", "JSON", "parse", "loadUser", "console", "logout", "login", "credentials", "response", "success", "setItem", "stringify", "Error", "message", "_error$response", "_error$response$data", "errorMessage", "data", "removeItem", "getCurrentUser", "_error$response2", "_error$response2$data", "updateUser", "userData", "updatedUser", "clearError", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "$RefreshReg$"], "sources": ["/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { authAPI } from '../services/api';\n\n// Initial state\nconst initialState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null,\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  LOGOUT: 'LOGOUT',\n  LOAD_USER_START: 'LOAD_USER_START',\n  LOAD_USER_SUCCESS: 'LOAD_USER_SUCCESS',\n  LOAD_USER_FAILURE: 'LOAD_USER_FAILURE',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n  UPDATE_USER: 'UPDATE_USER',\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n      return {\n        ...state,\n        isLoading: true,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n      };\n\n    case AUTH_ACTIONS.LOGOUT:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.LOAD_USER_START:\n      return {\n        ...state,\n        isLoading: true,\n      };\n\n    case AUTH_ACTIONS.LOAD_USER_SUCCESS:\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.LOAD_USER_FAILURE:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n      };\n\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.UPDATE_USER:\n      return {\n        ...state,\n        user: { ...state.user, ...action.payload },\n      };\n\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = createContext();\n\n// Auth provider component\nexport const AuthProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Load user on app start\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    const user = localStorage.getItem('user');\n\n    if (token && user) {\n      try {\n        const parsedUser = JSON.parse(user);\n        dispatch({\n          type: AUTH_ACTIONS.LOGIN_SUCCESS,\n          payload: { user: parsedUser, token },\n        });\n        \n        // Verify token is still valid\n        loadUser();\n      } catch (error) {\n        console.error('Error parsing stored user:', error);\n        logout();\n      }\n    } else {\n      dispatch({ type: AUTH_ACTIONS.LOAD_USER_FAILURE, payload: null });\n    }\n  }, []);\n\n  // Login function\n  const login = async (credentials) => {\n    try {\n      dispatch({ type: AUTH_ACTIONS.LOGIN_START });\n\n      const response = await authAPI.login(credentials);\n\n      if (response.success) {\n        const { user, token } = response;\n\n        // Store in localStorage\n        localStorage.setItem('token', token);\n        localStorage.setItem('user', JSON.stringify(user));\n\n        dispatch({\n          type: AUTH_ACTIONS.LOGIN_SUCCESS,\n          payload: { user, token },\n        });\n\n        return { success: true };\n      } else {\n        throw new Error(response.message || 'Login failed');\n      }\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || error.message || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage,\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Logout function\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    dispatch({ type: AUTH_ACTIONS.LOGOUT });\n  };\n\n  // Load current user\n  const loadUser = async () => {\n    try {\n      dispatch({ type: AUTH_ACTIONS.LOAD_USER_START });\n\n      const response = await authAPI.getCurrentUser();\n\n      if (response.success) {\n        dispatch({\n          type: AUTH_ACTIONS.LOAD_USER_SUCCESS,\n          payload: response.user,\n        });\n\n        // Update localStorage\n        localStorage.setItem('user', JSON.stringify(response.user));\n      } else {\n        throw new Error(response.message || 'Failed to load user');\n      }\n    } catch (error) {\n      console.error('Load user error:', error);\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_FAILURE,\n        payload: error.response?.data?.message || 'Failed to load user',\n      });\n      \n      // Clear invalid token\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n    }\n  };\n\n  // Update user data\n  const updateUser = (userData) => {\n    dispatch({\n      type: AUTH_ACTIONS.UPDATE_USER,\n      payload: userData,\n    });\n\n    // Update localStorage\n    const updatedUser = { ...state.user, ...userData };\n    localStorage.setItem('user', JSON.stringify(updatedUser));\n  };\n\n  // Clear error\n  const clearError = () => {\n    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });\n  };\n\n  // Context value\n  const value = {\n    ...state,\n    login,\n    logout,\n    loadUser,\n    updateUser,\n    clearError,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook to use auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC/E,SAASC,OAAO,QAAQ,iBAAiB;;AAEzC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG;EACnBC,WAAW,EAAE,aAAa;EAC1BC,aAAa,EAAE,eAAe;EAC9BC,aAAa,EAAE,eAAe;EAC9BC,MAAM,EAAE,QAAQ;EAChBC,eAAe,EAAE,iBAAiB;EAClCC,iBAAiB,EAAE,mBAAmB;EACtCC,iBAAiB,EAAE,mBAAmB;EACtCC,WAAW,EAAE,aAAa;EAC1BC,WAAW,EAAE;AACf,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKb,YAAY,CAACC,WAAW;MAC3B,OAAO;QACL,GAAGU,KAAK;QACRb,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACE,aAAa;MAC7B,OAAO;QACL,GAAGS,KAAK;QACRhB,IAAI,EAAEiB,MAAM,CAACE,OAAO,CAACnB,IAAI;QACzBC,KAAK,EAAEgB,MAAM,CAACE,OAAO,CAAClB,KAAK;QAC3BC,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACG,aAAa;MAC7B,OAAO;QACL,GAAGQ,KAAK;QACRhB,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEa,MAAM,CAACE;MAChB,CAAC;IAEH,KAAKd,YAAY,CAACI,MAAM;MACtB,OAAO;QACL,GAAGO,KAAK;QACRhB,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACK,eAAe;MAC/B,OAAO;QACL,GAAGM,KAAK;QACRb,SAAS,EAAE;MACb,CAAC;IAEH,KAAKE,YAAY,CAACM,iBAAiB;MACjC,OAAO;QACL,GAAGK,KAAK;QACRhB,IAAI,EAAEiB,MAAM,CAACE,OAAO;QACpBjB,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACO,iBAAiB;MACjC,OAAO;QACL,GAAGI,KAAK;QACRhB,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEa,MAAM,CAACE;MAChB,CAAC;IAEH,KAAKd,YAAY,CAACQ,WAAW;MAC3B,OAAO;QACL,GAAGG,KAAK;QACRZ,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACS,WAAW;MAC3B,OAAO;QACL,GAAGE,KAAK;QACRhB,IAAI,EAAE;UAAE,GAAGgB,KAAK,CAAChB,IAAI;UAAE,GAAGiB,MAAM,CAACE;QAAQ;MAC3C,CAAC;IAEH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMI,WAAW,gBAAG5B,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAM6B,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACP,KAAK,EAAEQ,QAAQ,CAAC,GAAG9B,UAAU,CAACqB,WAAW,EAAEhB,YAAY,CAAC;;EAE/D;EACAJ,SAAS,CAAC,MAAM;IACd,MAAMM,KAAK,GAAGwB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAM1B,IAAI,GAAGyB,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAEzC,IAAIzB,KAAK,IAAID,IAAI,EAAE;MACjB,IAAI;QACF,MAAM2B,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAC7B,IAAI,CAAC;QACnCwB,QAAQ,CAAC;UACPN,IAAI,EAAEb,YAAY,CAACE,aAAa;UAChCY,OAAO,EAAE;YAAEnB,IAAI,EAAE2B,UAAU;YAAE1B;UAAM;QACrC,CAAC,CAAC;;QAEF;QACA6B,QAAQ,CAAC,CAAC;MACZ,CAAC,CAAC,OAAO1B,KAAK,EAAE;QACd2B,OAAO,CAAC3B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD4B,MAAM,CAAC,CAAC;MACV;IACF,CAAC,MAAM;MACLR,QAAQ,CAAC;QAAEN,IAAI,EAAEb,YAAY,CAACO,iBAAiB;QAAEO,OAAO,EAAE;MAAK,CAAC,CAAC;IACnE;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMc,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACFV,QAAQ,CAAC;QAAEN,IAAI,EAAEb,YAAY,CAACC;MAAY,CAAC,CAAC;MAE5C,MAAM6B,QAAQ,GAAG,MAAMvC,OAAO,CAACqC,KAAK,CAACC,WAAW,CAAC;MAEjD,IAAIC,QAAQ,CAACC,OAAO,EAAE;QACpB,MAAM;UAAEpC,IAAI;UAAEC;QAAM,CAAC,GAAGkC,QAAQ;;QAEhC;QACAV,YAAY,CAACY,OAAO,CAAC,OAAO,EAAEpC,KAAK,CAAC;QACpCwB,YAAY,CAACY,OAAO,CAAC,MAAM,EAAET,IAAI,CAACU,SAAS,CAACtC,IAAI,CAAC,CAAC;QAElDwB,QAAQ,CAAC;UACPN,IAAI,EAAEb,YAAY,CAACE,aAAa;UAChCY,OAAO,EAAE;YAAEnB,IAAI;YAAEC;UAAM;QACzB,CAAC,CAAC;QAEF,OAAO;UAAEmC,OAAO,EAAE;QAAK,CAAC;MAC1B,CAAC,MAAM;QACL,MAAM,IAAIG,KAAK,CAACJ,QAAQ,CAACK,OAAO,IAAI,cAAc,CAAC;MACrD;IACF,CAAC,CAAC,OAAOpC,KAAK,EAAE;MAAA,IAAAqC,eAAA,EAAAC,oBAAA;MACd,MAAMC,YAAY,GAAG,EAAAF,eAAA,GAAArC,KAAK,CAAC+B,QAAQ,cAAAM,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBG,IAAI,cAAAF,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAIpC,KAAK,CAACoC,OAAO,IAAI,cAAc;MACrFhB,QAAQ,CAAC;QACPN,IAAI,EAAEb,YAAY,CAACG,aAAa;QAChCW,OAAO,EAAEwB;MACX,CAAC,CAAC;MACF,OAAO;QAAEP,OAAO,EAAE,KAAK;QAAEhC,KAAK,EAAEuC;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMX,MAAM,GAAGA,CAAA,KAAM;IACnBP,YAAY,CAACoB,UAAU,CAAC,OAAO,CAAC;IAChCpB,YAAY,CAACoB,UAAU,CAAC,MAAM,CAAC;IAC/BrB,QAAQ,CAAC;MAAEN,IAAI,EAAEb,YAAY,CAACI;IAAO,CAAC,CAAC;EACzC,CAAC;;EAED;EACA,MAAMqB,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFN,QAAQ,CAAC;QAAEN,IAAI,EAAEb,YAAY,CAACK;MAAgB,CAAC,CAAC;MAEhD,MAAMyB,QAAQ,GAAG,MAAMvC,OAAO,CAACkD,cAAc,CAAC,CAAC;MAE/C,IAAIX,QAAQ,CAACC,OAAO,EAAE;QACpBZ,QAAQ,CAAC;UACPN,IAAI,EAAEb,YAAY,CAACM,iBAAiB;UACpCQ,OAAO,EAAEgB,QAAQ,CAACnC;QACpB,CAAC,CAAC;;QAEF;QACAyB,YAAY,CAACY,OAAO,CAAC,MAAM,EAAET,IAAI,CAACU,SAAS,CAACH,QAAQ,CAACnC,IAAI,CAAC,CAAC;MAC7D,CAAC,MAAM;QACL,MAAM,IAAIuC,KAAK,CAACJ,QAAQ,CAACK,OAAO,IAAI,qBAAqB,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOpC,KAAK,EAAE;MAAA,IAAA2C,gBAAA,EAAAC,qBAAA;MACdjB,OAAO,CAAC3B,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCoB,QAAQ,CAAC;QACPN,IAAI,EAAEb,YAAY,CAACO,iBAAiB;QACpCO,OAAO,EAAE,EAAA4B,gBAAA,GAAA3C,KAAK,CAAC+B,QAAQ,cAAAY,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBH,IAAI,cAAAI,qBAAA,uBAApBA,qBAAA,CAAsBR,OAAO,KAAI;MAC5C,CAAC,CAAC;;MAEF;MACAf,YAAY,CAACoB,UAAU,CAAC,OAAO,CAAC;MAChCpB,YAAY,CAACoB,UAAU,CAAC,MAAM,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMI,UAAU,GAAIC,QAAQ,IAAK;IAC/B1B,QAAQ,CAAC;MACPN,IAAI,EAAEb,YAAY,CAACS,WAAW;MAC9BK,OAAO,EAAE+B;IACX,CAAC,CAAC;;IAEF;IACA,MAAMC,WAAW,GAAG;MAAE,GAAGnC,KAAK,CAAChB,IAAI;MAAE,GAAGkD;IAAS,CAAC;IAClDzB,YAAY,CAACY,OAAO,CAAC,MAAM,EAAET,IAAI,CAACU,SAAS,CAACa,WAAW,CAAC,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB5B,QAAQ,CAAC;MAAEN,IAAI,EAAEb,YAAY,CAACQ;IAAY,CAAC,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMwC,KAAK,GAAG;IACZ,GAAGrC,KAAK;IACRiB,KAAK;IACLD,MAAM;IACNF,QAAQ;IACRmB,UAAU;IACVG;EACF,CAAC;EAED,oBACEtD,OAAA,CAACsB,WAAW,CAACkC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA/B,QAAA,EAChCA;EAAQ;IAAAiC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAAnC,EAAA,CApIaF,YAAY;AAAAsC,EAAA,GAAZtC,YAAY;AAqIzB,OAAO,MAAMuC,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAGrE,UAAU,CAAC2B,WAAW,CAAC;EACvC,IAAI,CAAC0C,OAAO,EAAE;IACZ,MAAM,IAAIvB,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOuB,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAexC,WAAW;AAAC,IAAAuC,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}