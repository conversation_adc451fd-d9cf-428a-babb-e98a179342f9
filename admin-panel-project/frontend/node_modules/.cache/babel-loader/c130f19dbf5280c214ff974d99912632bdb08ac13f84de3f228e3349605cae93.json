{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Sidebar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { NavLink, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  isCollapsed,\n  onToggle\n}) => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const menuItems = [{\n    path: '/dashboard',\n    icon: '📊',\n    label: 'Dashboard',\n    roles: ['superadmin', 'admin', 'agent']\n  }, {\n    path: '/users',\n    icon: '👥',\n    label: 'User Management',\n    roles: ['superadmin', 'admin']\n  }, {\n    path: '/analytics',\n    icon: '📈',\n    label: 'Analytics',\n    roles: ['superadmin', 'admin', 'agent']\n  }, {\n    path: '/reports',\n    icon: '📋',\n    label: 'Reports',\n    roles: ['superadmin', 'admin']\n  }, {\n    path: '/settings',\n    icon: '⚙️',\n    label: 'Settings',\n    roles: ['superadmin', 'admin']\n  }];\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n  const handleProfile = () => {\n    navigate('/profile');\n  };\n  const handleSettings = () => {\n    navigate('/settings');\n  };\n\n  // Filter menu items based on user role\n  const filteredMenuItems = menuItems.filter(item => item.roles.includes(user === null || user === void 0 ? void 0 : user.role));\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"sidebar-toggle\",\n      onClick: onToggle,\n      \"aria-label\": \"Toggle sidebar\",\n      children: \"\\u2630\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `sidebar ${isCollapsed ? 'collapsed' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo\",\n          children: \"Admin Panel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"sidebar-menu\",\n        children: filteredMenuItems.map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n          to: item.path,\n          className: ({\n            isActive\n          }) => `menu-item ${isActive ? 'active' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"menu-icon\",\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 15\n          }, this), item.label]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-footer\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-name\",\n            children: user === null || user === void 0 ? void 0 : user.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-role\",\n            children: user === null || user === void 0 ? void 0 : user.role\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-btn\",\n            onClick: handleSettings,\n            title: \"Settings\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tooltip\",\n              children: \"Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-btn\",\n            onClick: handleProfile,\n            title: \"Profile\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tooltip\",\n              children: \"Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-btn\",\n            onClick: handleLogout,\n            title: \"Logout\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\uD83D\\uDEAA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tooltip\",\n              children: \"Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Sidebar, \"owExUWFylk0vVlQUKU4QcBpCg7Y=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "NavLink", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Sidebar", "isCollapsed", "onToggle", "_s", "user", "logout", "navigate", "menuItems", "path", "icon", "label", "roles", "handleLogout", "handleProfile", "handleSettings", "filteredMenuItems", "filter", "item", "includes", "role", "children", "className", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "to", "isActive", "name", "title", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Sidebar.js"], "sourcesContent": ["import React from 'react';\nimport { NavLink, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\n\nconst Sidebar = ({ isCollapsed, onToggle }) => {\n  const { user, logout } = useAuth();\n  const navigate = useNavigate();\n\n  const menuItems = [\n    {\n      path: '/dashboard',\n      icon: '📊',\n      label: 'Dashboard',\n      roles: ['superadmin', 'admin', 'agent'],\n    },\n    {\n      path: '/users',\n      icon: '👥',\n      label: 'User Management',\n      roles: ['superadmin', 'admin'],\n    },\n    {\n      path: '/analytics',\n      icon: '📈',\n      label: 'Analytics',\n      roles: ['superadmin', 'admin', 'agent'],\n    },\n    {\n      path: '/reports',\n      icon: '📋',\n      label: 'Reports',\n      roles: ['superadmin', 'admin'],\n    },\n    {\n      path: '/settings',\n      icon: '⚙️',\n      label: 'Settings',\n      roles: ['superadmin', 'admin'],\n    },\n  ];\n\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n\n  const handleProfile = () => {\n    navigate('/profile');\n  };\n\n  const handleSettings = () => {\n    navigate('/settings');\n  };\n\n  // Filter menu items based on user role\n  const filteredMenuItems = menuItems.filter(item => \n    item.roles.includes(user?.role)\n  );\n\n  return (\n    <>\n      <button\n        className=\"sidebar-toggle\"\n        onClick={onToggle}\n        aria-label=\"Toggle sidebar\"\n      >\n        ☰\n      </button>\n\n      <div className={`sidebar ${isCollapsed ? 'collapsed' : ''}`}>\n        <div className=\"sidebar-header\">\n          <div className=\"logo\">\n            Admin Panel\n          </div>\n        </div>\n\n        <nav className=\"sidebar-menu\">\n          {filteredMenuItems.map((item) => (\n            <NavLink\n              key={item.path}\n              to={item.path}\n              className={({ isActive }) => \n                `menu-item ${isActive ? 'active' : ''}`\n              }\n            >\n              <span className=\"menu-icon\">{item.icon}</span>\n              {item.label}\n            </NavLink>\n          ))}\n        </nav>\n\n        <div className=\"sidebar-footer\">\n          <div className=\"user-info\">\n            <div className=\"user-name\">{user?.name}</div>\n            <div className=\"user-role\">{user?.role}</div>\n          </div>\n          \n          <div className=\"footer-actions\">\n            <button\n              className=\"action-btn\"\n              onClick={handleSettings}\n              title=\"Settings\"\n            >\n              <span>⚙️</span>\n              <div className=\"tooltip\">Settings</div>\n            </button>\n            \n            <button\n              className=\"action-btn\"\n              onClick={handleProfile}\n              title=\"Profile\"\n            >\n              <span>👤</span>\n              <div className=\"tooltip\">Profile</div>\n            </button>\n            \n            <button\n              className=\"action-btn\"\n              onClick={handleLogout}\n              title=\"Logout\"\n            >\n              <span>🚪</span>\n              <div className=\"tooltip\">Logout</div>\n            </button>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,WAAW,QAAQ,kBAAkB;AACvD,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,OAAO,GAAGA,CAAC;EAAEC,WAAW;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC7C,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGV,OAAO,CAAC,CAAC;EAClC,MAAMW,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAE9B,MAAMa,SAAS,GAAG,CAChB;IACEC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,OAAO;EACxC,CAAC,EACD;IACEH,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO;EAC/B,CAAC,EACD;IACEH,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,OAAO;EACxC,CAAC,EACD;IACEH,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO;EAC/B,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO;EAC/B,CAAC,CACF;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBP,MAAM,CAAC,CAAC;IACRC,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMO,aAAa,GAAGA,CAAA,KAAM;IAC1BP,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;EAED,MAAMQ,cAAc,GAAGA,CAAA,KAAM;IAC3BR,QAAQ,CAAC,WAAW,CAAC;EACvB,CAAC;;EAED;EACA,MAAMS,iBAAiB,GAAGR,SAAS,CAACS,MAAM,CAACC,IAAI,IAC7CA,IAAI,CAACN,KAAK,CAACO,QAAQ,CAACd,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,IAAI,CAChC,CAAC;EAED,oBACEtB,OAAA,CAAAE,SAAA;IAAAqB,QAAA,gBACEvB,OAAA;MACEwB,SAAS,EAAC,gBAAgB;MAC1BC,OAAO,EAAEpB,QAAS;MAClB,cAAW,gBAAgB;MAAAkB,QAAA,EAC5B;IAED;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAET7B,OAAA;MAAKwB,SAAS,EAAE,WAAWpB,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;MAAAmB,QAAA,gBAC1DvB,OAAA;QAAKwB,SAAS,EAAC,gBAAgB;QAAAD,QAAA,eAC7BvB,OAAA;UAAKwB,SAAS,EAAC,MAAM;UAAAD,QAAA,EAAC;QAEtB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7B,OAAA;QAAKwB,SAAS,EAAC,cAAc;QAAAD,QAAA,EAC1BL,iBAAiB,CAACY,GAAG,CAAEV,IAAI,iBAC1BpB,OAAA,CAACJ,OAAO;UAENmC,EAAE,EAAEX,IAAI,CAACT,IAAK;UACda,SAAS,EAAEA,CAAC;YAAEQ;UAAS,CAAC,KACtB,aAAaA,QAAQ,GAAG,QAAQ,GAAG,EAAE,EACtC;UAAAT,QAAA,gBAEDvB,OAAA;YAAMwB,SAAS,EAAC,WAAW;YAAAD,QAAA,EAAEH,IAAI,CAACR;UAAI;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAC7CT,IAAI,CAACP,KAAK;QAAA,GAPNO,IAAI,CAACT,IAAI;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQP,CACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7B,OAAA;QAAKwB,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAC7BvB,OAAA;UAAKwB,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBvB,OAAA;YAAKwB,SAAS,EAAC,WAAW;YAAAD,QAAA,EAAEhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B;UAAI;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C7B,OAAA;YAAKwB,SAAS,EAAC,WAAW;YAAAD,QAAA,EAAEhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe;UAAI;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eAEN7B,OAAA;UAAKwB,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC7BvB,OAAA;YACEwB,SAAS,EAAC,YAAY;YACtBC,OAAO,EAAER,cAAe;YACxBiB,KAAK,EAAC,UAAU;YAAAX,QAAA,gBAEhBvB,OAAA;cAAAuB,QAAA,EAAM;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACf7B,OAAA;cAAKwB,SAAS,EAAC,SAAS;cAAAD,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eAET7B,OAAA;YACEwB,SAAS,EAAC,YAAY;YACtBC,OAAO,EAAET,aAAc;YACvBkB,KAAK,EAAC,SAAS;YAAAX,QAAA,gBAEfvB,OAAA;cAAAuB,QAAA,EAAM;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACf7B,OAAA;cAAKwB,SAAS,EAAC,SAAS;cAAAD,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eAET7B,OAAA;YACEwB,SAAS,EAAC,YAAY;YACtBC,OAAO,EAAEV,YAAa;YACtBmB,KAAK,EAAC,QAAQ;YAAAX,QAAA,gBAEdvB,OAAA;cAAAuB,QAAA,EAAM;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACf7B,OAAA;cAAKwB,SAAS,EAAC,SAAS;cAAAD,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACvB,EAAA,CA7HIH,OAAO;EAAA,QACcL,OAAO,EACfD,WAAW;AAAA;AAAAsC,EAAA,GAFxBhC,OAAO;AA+Hb,eAAeA,OAAO;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}