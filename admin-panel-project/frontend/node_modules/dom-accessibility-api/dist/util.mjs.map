{"version": 3, "file": "util.mjs", "names": ["getLocalName", "getRole", "isElement", "node", "nodeType", "ELEMENT_NODE", "isHTMLTableCaptionElement", "isHTMLInputElement", "isHTMLOptGroupElement", "isHTMLSelectElement", "isHTMLTableElement", "isHTMLTextAreaElement", "safeWindow", "ownerDocument", "defaultView", "TypeError", "isHTMLFieldSetElement", "isHTMLLegendElement", "isHTMLSlotElement", "isSVGElement", "ownerSVGElement", "undefined", "isSVGSVGElement", "isSVGTitleElement", "queryIdRefs", "attributeName", "hasAttribute", "ids", "getAttribute", "split", "root", "getRootNode", "map", "id", "getElementById", "filter", "element", "hasAnyConcreteRoles", "roles", "indexOf"], "sources": ["../sources/util.ts"], "sourcesContent": ["export { getLocalName } from \"./getRole\";\nimport getRole, { getLocalName } from \"./getRole\";\n\nexport function isElement(node: Node | null): node is Element {\n\treturn node !== null && node.nodeType === node.ELEMENT_NODE;\n}\n\nexport function isHTMLTableCaptionElement(\n\tnode: Node | null\n): node is HTMLTableCaptionElement {\n\treturn isElement(node) && getLocalName(node) === \"caption\";\n}\n\nexport function isHTMLInputElement(\n\tnode: Node | null\n): node is HTMLInputElement {\n\treturn isElement(node) && getLocalName(node) === \"input\";\n}\n\nexport function isHTMLOptGroupElement(\n\tnode: Node | null\n): node is HTMLOptGroupElement {\n\treturn isElement(node) && getLocalName(node) === \"optgroup\";\n}\n\nexport function isHTMLSelectElement(\n\tnode: Node | null\n): node is HTMLSelectElement {\n\treturn isElement(node) && getLocalName(node) === \"select\";\n}\n\nexport function isHTMLTableElement(\n\tnode: Node | null\n): node is HTMLTableElement {\n\treturn isElement(node) && getLocalName(node) === \"table\";\n}\n\nexport function isHTMLTextAreaElement(\n\tnode: Node | null\n): node is HTMLTextAreaElement {\n\treturn isElement(node) && getLocalName(node) === \"textarea\";\n}\n\nexport function safeWindow(node: Node): Window {\n\tconst { defaultView } =\n\t\tnode.ownerDocument === null ? (node as Document) : node.ownerDocument;\n\n\tif (defaultView === null) {\n\t\tthrow new TypeError(\"no window available\");\n\t}\n\treturn defaultView;\n}\n\nexport function isHTMLFieldSetElement(\n\tnode: Node | null\n): node is HTMLFieldSetElement {\n\treturn isElement(node) && getLocalName(node) === \"fieldset\";\n}\n\nexport function isHTMLLegendElement(\n\tnode: Node | null\n): node is HTMLLegendElement {\n\treturn isElement(node) && getLocalName(node) === \"legend\";\n}\n\nexport function isHTMLSlotElement(node: Node | null): node is HTMLSlotElement {\n\treturn isElement(node) && getLocalName(node) === \"slot\";\n}\n\nexport function isSVGElement(node: Node | null): node is SVGElement {\n\treturn isElement(node) && (node as SVGElement).ownerSVGElement !== undefined;\n}\n\nexport function isSVGSVGElement(node: Node | null): node is SVGSVGElement {\n\treturn isElement(node) && getLocalName(node) === \"svg\";\n}\n\nexport function isSVGTitleElement(node: Node | null): node is SVGTitleElement {\n\treturn isSVGElement(node) && getLocalName(node) === \"title\";\n}\n\n/**\n *\n * @param {Node} node -\n * @param {string} attributeName -\n * @returns {Element[]} -\n */\nexport function queryIdRefs(node: Node, attributeName: string): Element[] {\n\tif (isElement(node) && node.hasAttribute(attributeName)) {\n\t\t// eslint-disable-next-line @typescript-eslint/no-non-null-assertion -- safe due to hasAttribute check\n\t\tconst ids = node.getAttribute(attributeName)!.split(\" \");\n\n\t\t// Browsers that don't support shadow DOM won't have getRootNode\n\t\tconst root = node.getRootNode\n\t\t\t? (node.getRootNode() as Document | ShadowRoot)\n\t\t\t: node.ownerDocument;\n\n\t\treturn ids\n\t\t\t.map((id) => root.getElementById(id))\n\t\t\t.filter(\n\t\t\t\t(element: Element | null): element is Element => element !== null\n\t\t\t\t// TODO: why does this not narrow?\n\t\t\t) as Element[];\n\t}\n\n\treturn [];\n}\n\nexport function hasAnyConcreteRoles(\n\tnode: Node,\n\troles: Array<string | null>\n): node is Element {\n\tif (isElement(node)) {\n\t\treturn roles.indexOf(getRole(node)) !== -1;\n\t}\n\treturn false;\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,eAAW;AACxC,OAAOC,OAAO,IAAID,YAAY,QAAQ,eAAW;AAEjD,OAAO,SAASE,SAAS,CAACC,IAAiB,EAAmB;EAC7D,OAAOA,IAAI,KAAK,IAAI,IAAIA,IAAI,CAACC,QAAQ,KAAKD,IAAI,CAACE,YAAY;AAC5D;AAEA,OAAO,SAASC,yBAAyB,CACxCH,IAAiB,EACiB;EAClC,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIH,YAAY,CAACG,IAAI,CAAC,KAAK,SAAS;AAC3D;AAEA,OAAO,SAASI,kBAAkB,CACjCJ,IAAiB,EACU;EAC3B,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIH,YAAY,CAACG,IAAI,CAAC,KAAK,OAAO;AACzD;AAEA,OAAO,SAASK,qBAAqB,CACpCL,IAAiB,EACa;EAC9B,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIH,YAAY,CAACG,IAAI,CAAC,KAAK,UAAU;AAC5D;AAEA,OAAO,SAASM,mBAAmB,CAClCN,IAAiB,EACW;EAC5B,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIH,YAAY,CAACG,IAAI,CAAC,KAAK,QAAQ;AAC1D;AAEA,OAAO,SAASO,kBAAkB,CACjCP,IAAiB,EACU;EAC3B,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIH,YAAY,CAACG,IAAI,CAAC,KAAK,OAAO;AACzD;AAEA,OAAO,SAASQ,qBAAqB,CACpCR,IAAiB,EACa;EAC9B,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIH,YAAY,CAACG,IAAI,CAAC,KAAK,UAAU;AAC5D;AAEA,OAAO,SAASS,UAAU,CAACT,IAAU,EAAU;EAC9C,WACCA,IAAI,CAACU,aAAa,KAAK,IAAI,GAAIV,IAAI,GAAgBA,IAAI,CAACU,aAAa;IAD9DC,WAAW,QAAXA,WAAW;EAGnB,IAAIA,WAAW,KAAK,IAAI,EAAE;IACzB,MAAM,IAAIC,SAAS,CAAC,qBAAqB,CAAC;EAC3C;EACA,OAAOD,WAAW;AACnB;AAEA,OAAO,SAASE,qBAAqB,CACpCb,IAAiB,EACa;EAC9B,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIH,YAAY,CAACG,IAAI,CAAC,KAAK,UAAU;AAC5D;AAEA,OAAO,SAASc,mBAAmB,CAClCd,IAAiB,EACW;EAC5B,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIH,YAAY,CAACG,IAAI,CAAC,KAAK,QAAQ;AAC1D;AAEA,OAAO,SAASe,iBAAiB,CAACf,IAAiB,EAA2B;EAC7E,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIH,YAAY,CAACG,IAAI,CAAC,KAAK,MAAM;AACxD;AAEA,OAAO,SAASgB,YAAY,CAAChB,IAAiB,EAAsB;EACnE,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAKA,IAAI,CAAgBiB,eAAe,KAAKC,SAAS;AAC7E;AAEA,OAAO,SAASC,eAAe,CAACnB,IAAiB,EAAyB;EACzE,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIH,YAAY,CAACG,IAAI,CAAC,KAAK,KAAK;AACvD;AAEA,OAAO,SAASoB,iBAAiB,CAACpB,IAAiB,EAA2B;EAC7E,OAAOgB,YAAY,CAAChB,IAAI,CAAC,IAAIH,YAAY,CAACG,IAAI,CAAC,KAAK,OAAO;AAC5D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASqB,WAAW,CAACrB,IAAU,EAAEsB,aAAqB,EAAa;EACzE,IAAIvB,SAAS,CAACC,IAAI,CAAC,IAAIA,IAAI,CAACuB,YAAY,CAACD,aAAa,CAAC,EAAE;IACxD;IACA,IAAME,GAAG,GAAGxB,IAAI,CAACyB,YAAY,CAACH,aAAa,CAAC,CAAEI,KAAK,CAAC,GAAG,CAAC;;IAExD;IACA,IAAMC,IAAI,GAAG3B,IAAI,CAAC4B,WAAW,GACzB5B,IAAI,CAAC4B,WAAW,EAAE,GACnB5B,IAAI,CAACU,aAAa;IAErB,OAAOc,GAAG,CACRK,GAAG,CAAC,UAACC,EAAE;MAAA,OAAKH,IAAI,CAACI,cAAc,CAACD,EAAE,CAAC;IAAA,EAAC,CACpCE,MAAM,CACN,UAACC,OAAuB;MAAA,OAAyBA,OAAO,KAAK,IAAI;IAAA;IACjE;IAAA,CACA;EACH;;EAEA,OAAO,EAAE;AACV;AAEA,OAAO,SAASC,mBAAmB,CAClClC,IAAU,EACVmC,KAA2B,EACT;EAClB,IAAIpC,SAAS,CAACC,IAAI,CAAC,EAAE;IACpB,OAAOmC,KAAK,CAACC,OAAO,CAACtC,OAAO,CAACE,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;EAC3C;EACA,OAAO,KAAK;AACb"}