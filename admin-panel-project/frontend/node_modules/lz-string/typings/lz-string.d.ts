export function compressToBase<PERSON>(input: string): string;
export function decompress<PERSON><PERSON><PERSON><PERSON><PERSON>(input: string): string;

export function compressToUTF16(input: string): string;
export function decompressFromUTF16(compressed: string): string;

export function compressToUint8Array(uncompressed: string): Uint8Array;
export function decompressFromUint8Array(compressed: Uint8Array): string;

export function compressToEncodedURIComponent(input: string): string;
export function decompressFromEncodedURIComponent(compressed: string): string;

export function compress(input: string): string;
export function decompress(compressed: string): string;
