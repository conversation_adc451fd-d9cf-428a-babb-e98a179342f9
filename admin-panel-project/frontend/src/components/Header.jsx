import React, { useState } from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const Header = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [showUserMenu, setShowUserMenu] = useState(false);

  const menuItems = [
    {
      path: '/dashboard',
      icon: '📊',
      label: 'Dashboard',
      roles: ['superadmin', 'admin', 'agent'],
    },
    {
      path: '/users',
      icon: '👥',
      label: 'Users',
      roles: ['superadmin', 'admin'],
    },
    {
      path: '/analytics',
      icon: '📈',
      label: 'Analytics',
      roles: ['superadmin', 'admin', 'agent'],
    },
    {
      path: '/reports',
      icon: '📋',
      label: 'Reports',
      roles: ['superadmin', 'admin'],
    },
    {
      path: '/settings',
      icon: '⚙️',
      label: 'Settings',
      roles: ['superadmin', 'admin'],
    },
  ];

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const handleProfile = () => {
    navigate('/profile');
    setShowUserMenu(false);
  };

  const handleSettings = () => {
    navigate('/settings');
    setShowUserMenu(false);
  };

  // Filter menu items based on user role
  const filteredMenuItems = menuItems.filter(item => 
    item.roles.includes(user?.role)
  );

  const getRoleColor = (role) => {
    switch (role) {
      case 'superadmin':
        return '#ef4444';
      case 'admin':
        return '#3b82f6';
      case 'agent':
        return '#10b981';
      default:
        return '#64748b';
    }
  };

  return (
    <header className="header">
      <div className="header-container">
        {/* Logo */}
        <div className="header-logo">
          <div className="logo-icon">AP</div>
          <span className="logo-text">Admin Panel</span>
        </div>

        {/* Navigation Menu */}
        <nav className="header-nav">
          {filteredMenuItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              className={({ isActive }) => 
                `nav-item ${isActive ? 'active' : ''}`
              }
            >
              <span className="nav-icon">{item.icon}</span>
              <span className="nav-label">{item.label}</span>
            </NavLink>
          ))}
        </nav>

        {/* User Menu */}
        <div className="header-user">
          <div 
            className="user-profile"
            onClick={() => setShowUserMenu(!showUserMenu)}
          >
            <div className="user-avatar">
              {user?.name?.charAt(0)?.toUpperCase()}
            </div>
            <div className="user-info">
              <div className="user-name">{user?.name}</div>
              <div 
                className="user-role"
                style={{ color: getRoleColor(user?.role) }}
              >
                {user?.role}
              </div>
            </div>
            <div className="dropdown-arrow">▼</div>
          </div>

          {/* Dropdown Menu */}
          {showUserMenu && (
            <div className="user-dropdown">
              <button 
                className="dropdown-item"
                onClick={handleProfile}
              >
                <span className="dropdown-icon">👤</span>
                Profile
              </button>
              <button 
                className="dropdown-item"
                onClick={handleSettings}
              >
                <span className="dropdown-icon">⚙️</span>
                Settings
              </button>
              <div className="dropdown-divider"></div>
              <button 
                className="dropdown-item logout"
                onClick={handleLogout}
              >
                <span className="dropdown-icon">🚪</span>
                Logout
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Click outside to close dropdown */}
      {showUserMenu && (
        <div 
          className="dropdown-overlay"
          onClick={() => setShowUserMenu(false)}
        ></div>
      )}
    </header>
  );
};

export default Header;
