import React from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const Sidebar = ({ isCollapsed, onToggle }) => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const menuItems = [
    {
      path: '/dashboard',
      icon: '📊',
      label: 'Dashboard',
      roles: ['superadmin', 'admin', 'agent'],
    },
    {
      path: '/users',
      icon: '👥',
      label: 'User Management',
      roles: ['superadmin', 'admin'],
    },
    {
      path: '/analytics',
      icon: '📈',
      label: 'Analytics',
      roles: ['superadmin', 'admin', 'agent'],
    },
    {
      path: '/reports',
      icon: '📋',
      label: 'Reports',
      roles: ['superadmin', 'admin'],
    },
    {
      path: '/settings',
      icon: '⚙️',
      label: 'Settings',
      roles: ['superadmin', 'admin'],
    },
  ];

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const handleProfile = () => {
    navigate('/profile');
  };

  const handleSettings = () => {
    navigate('/settings');
  };

  // Filter menu items based on user role
  const filteredMenuItems = menuItems.filter(item => 
    item.roles.includes(user?.role)
  );

  return (
    <>
      <button
        className="sidebar-toggle"
        onClick={onToggle}
        aria-label="Toggle sidebar"
      >
        ☰
      </button>

      <div className={`sidebar ${isCollapsed ? 'collapsed' : ''}`}>
        <div className="sidebar-header">
          <div className="logo">
            Admin Panel
          </div>
        </div>

        <nav className="sidebar-menu">
          {filteredMenuItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              className={({ isActive }) => 
                `menu-item ${isActive ? 'active' : ''}`
              }
            >
              <span className="menu-icon">{item.icon}</span>
              {item.label}
            </NavLink>
          ))}
        </nav>

        <div className="sidebar-footer">
          <div className="user-info">
            <div className="user-name">{user?.name}</div>
            <div className="user-role">{user?.role}</div>
          </div>
          
          <div className="footer-actions">
            <button
              className="action-btn"
              onClick={handleSettings}
              title="Settings"
            >
              <span>⚙️</span>
              <div className="tooltip">Settings</div>
            </button>
            
            <button
              className="action-btn"
              onClick={handleProfile}
              title="Profile"
            >
              <span>👤</span>
              <div className="tooltip">Profile</div>
            </button>
            
            <button
              className="action-btn"
              onClick={handleLogout}
              title="Logout"
            >
              <span>🚪</span>
              <div className="tooltip">Logout</div>
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
