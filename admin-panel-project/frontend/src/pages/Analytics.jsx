import React from 'react';
import { useAuth } from '../context/AuthContext';

const Analytics = () => {
  const { user } = useAuth();

  return (
    <div style={{ padding: '2rem' }}>
      {/* Header */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-header">
          <h1 className="card-title">Analytics Dashboard</h1>
        </div>
        <div className="card-body">
          <p style={{ color: 'var(--text-secondary)' }}>
            Welcome to the analytics section, {user?.name}. Here you can view detailed analytics and insights.
          </p>
        </div>
      </div>

      {/* Analytics Cards */}
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1.5rem', marginBottom: '2rem' }}>
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">User Activity</h3>
          </div>
          <div className="card-body">
            <div style={{ textAlign: 'center', padding: '2rem' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📊</div>
              <p style={{ color: 'var(--text-secondary)' }}>
                User activity analytics will be displayed here
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Performance Metrics</h3>
          </div>
          <div className="card-body">
            <div style={{ textAlign: 'center', padding: '2rem' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📈</div>
              <p style={{ color: 'var(--text-secondary)' }}>
                Performance metrics will be displayed here
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="card-title">System Health</h3>
          </div>
          <div className="card-body">
            <div style={{ textAlign: 'center', padding: '2rem' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>💚</div>
              <p style={{ color: 'var(--text-secondary)' }}>
                System health monitoring will be displayed here
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Coming Soon */}
      <div className="card">
        <div className="card-body">
          <div style={{ textAlign: 'center', padding: '3rem' }}>
            <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>🚀</div>
            <h2 style={{ marginBottom: '1rem', color: 'var(--text-primary)' }}>
              Advanced Analytics Coming Soon
            </h2>
            <p style={{ color: 'var(--text-secondary)', fontSize: '1.1rem' }}>
              We're working on bringing you comprehensive analytics including charts, 
              real-time data, and detailed insights.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
