import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { usersAPI } from '../services/api';

const Dashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    adminUsers: 0,
    agentUsers: 0,
  });
  const [recentUsers, setRecentUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load users data for stats
      const response = await usersAPI.getUsers({ limit: 10 });
      
      if (response.success) {
        const users = response.data;
        
        // Calculate stats
        const totalUsers = response.pagination.total;
        const activeUsers = users.filter(u => u.isActive).length;
        const adminUsers = users.filter(u => u.role === 'admin').length;
        const agentUsers = users.filter(u => u.role === 'agent').length;
        
        setStats({
          totalUsers,
          activeUsers,
          adminUsers,
          agentUsers,
        });
        
        setRecentUsers(users.slice(0, 5));
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'superadmin':
        return '#ef4444';
      case 'admin':
        return '#3b82f6';
      case 'agent':
        return '#10b981';
      default:
        return '#64748b';
    }
  };

  if (loading) {
    return (
      <div className="content">
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
          <div>Loading dashboard...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="content">
      {/* Welcome Section */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-body">
          <h1 style={{ fontSize: '2rem', fontWeight: '700', marginBottom: '0.5rem', color: 'var(--text-primary)' }}>
            {getGreeting()}, {user?.name}! 👋
          </h1>
          <p style={{ color: 'var(--text-secondary)', fontSize: '1.1rem' }}>
            Welcome to your admin dashboard. Here's what's happening today.
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem', marginBottom: '2rem' }}>
        <div className="card">
          <div className="card-body">
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>
                <p style={{ color: 'var(--text-secondary)', fontSize: '0.875rem', marginBottom: '0.5rem' }}>
                  Total Users
                </p>
                <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--text-primary)', margin: 0 }}>
                  {stats.totalUsers}
                </p>
              </div>
              <div style={{ fontSize: '2rem', opacity: 0.6 }}>👥</div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>
                <p style={{ color: 'var(--text-secondary)', fontSize: '0.875rem', marginBottom: '0.5rem' }}>
                  Active Users
                </p>
                <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--success-color)', margin: 0 }}>
                  {stats.activeUsers}
                </p>
              </div>
              <div style={{ fontSize: '2rem', opacity: 0.6 }}>✅</div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>
                <p style={{ color: 'var(--text-secondary)', fontSize: '0.875rem', marginBottom: '0.5rem' }}>
                  Administrators
                </p>
                <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--primary-color)', margin: 0 }}>
                  {stats.adminUsers}
                </p>
              </div>
              <div style={{ fontSize: '2rem', opacity: 0.6 }}>👑</div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>
                <p style={{ color: 'var(--text-secondary)', fontSize: '0.875rem', marginBottom: '0.5rem' }}>
                  Agents
                </p>
                <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--warning-color)', margin: 0 }}>
                  {stats.agentUsers}
                </p>
              </div>
              <div style={{ fontSize: '2rem', opacity: 0.6 }}>🎯</div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Users */}
      {(user?.role === 'superadmin' || user?.role === 'admin') && (
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">Recent Users</h2>
            <button 
              className="btn btn-primary btn-sm"
              onClick={() => window.location.href = '/users'}
            >
              View All
            </button>
          </div>
          <div className="card-body">
            {recentUsers.length > 0 ? (
              <div style={{ overflowX: 'auto' }}>
                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                  <thead>
                    <tr style={{ borderBottom: '2px solid var(--border-color)' }}>
                      <th style={{ padding: '1rem', textAlign: 'left', color: 'var(--text-secondary)', fontWeight: '600' }}>
                        User
                      </th>
                      <th style={{ padding: '1rem', textAlign: 'left', color: 'var(--text-secondary)', fontWeight: '600' }}>
                        Role
                      </th>
                      <th style={{ padding: '1rem', textAlign: 'left', color: 'var(--text-secondary)', fontWeight: '600' }}>
                        Status
                      </th>
                      <th style={{ padding: '1rem', textAlign: 'left', color: 'var(--text-secondary)', fontWeight: '600' }}>
                        Created
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {recentUsers.map((user) => (
                      <tr key={user._id} style={{ borderBottom: '1px solid var(--border-color)' }}>
                        <td style={{ padding: '1rem' }}>
                          <div>
                            <div style={{ fontWeight: '500', color: 'var(--text-primary)' }}>
                              {user.name}
                            </div>
                            <div style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>
                              {user.email}
                            </div>
                          </div>
                        </td>
                        <td style={{ padding: '1rem' }}>
                          <span
                            style={{
                              padding: '0.25rem 0.75rem',
                              borderRadius: '9999px',
                              fontSize: '0.75rem',
                              fontWeight: '500',
                              backgroundColor: getRoleColor(user.role) + '20',
                              color: getRoleColor(user.role),
                            }}
                          >
                            {user.role}
                          </span>
                        </td>
                        <td style={{ padding: '1rem' }}>
                          <span
                            style={{
                              padding: '0.25rem 0.75rem',
                              borderRadius: '9999px',
                              fontSize: '0.75rem',
                              fontWeight: '500',
                              backgroundColor: user.isActive ? 'var(--success-color)20' : 'var(--error-color)20',
                              color: user.isActive ? 'var(--success-color)' : 'var(--error-color)',
                            }}
                          >
                            {user.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td style={{ padding: '1rem', color: 'var(--text-secondary)' }}>
                          {formatDate(user.createdAt)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '2rem', color: 'var(--text-secondary)' }}>
                No users found
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
