import React, { useState, useEffect } from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import "../styles/login.scss";

const Login = () => {
  const { login, isAuthenticated, isLoading, error, clearError } = useAuth();
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    rememberMe: false,
  });
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Default credentials for autofill
  const defaultCredentials = [
    { label: "Super Admin", username: "superadmin", password: "admin123" },
    { label: "Admin", username: "admin", password: "admin123" },
    { label: "Agent", username: "agent", password: "admin123" },
  ];

  // Clear error when component mounts
  useEffect(() => {
    clearError();
  }, [clearError]);

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));

    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Handle autofill
  const handleAutofill = (credentials) => {
    setFormData((prev) => ({
      ...prev,
      username: credentials.username,
      password: credentials.password,
    }));
    setFormErrors({});
  };

  // Validate form
  const validateForm = () => {
    const errors = {};

    if (!formData.username.trim()) {
      errors.username = "Username is required";
    }

    if (!formData.password) {
      errors.password = "Password is required";
    } else if (formData.password.length < 6) {
      errors.password = "Password must be at least 6 characters";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    clearError();

    try {
      const result = await login({
        username: formData.username.trim(),
        password: formData.password,
      });

      if (result.success) {
        // Login successful, redirect will happen automatically
        console.log("Login successful");
      }
    } catch (err) {
      console.error("Login error:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <div className="login-logo">AP</div>
          <h1 className="login-title">Admin Panel</h1>
          <p className="login-subtitle">Sign in to your account</p>
        </div>

        <div className="login-body">
          {error && (
            <div className="alert alert-error">
              <span>⚠️</span>
              {error}
            </div>
          )}

          <form className="login-form" onSubmit={handleSubmit}>
            <div className="autofill-section">
              <div className="autofill-title">Quick Login</div>
              <div className="autofill-buttons">
                {defaultCredentials.map((cred, index) => (
                  <button
                    key={index}
                    type="button"
                    className="autofill-btn"
                    onClick={() => handleAutofill(cred)}
                  >
                    {cred.label}
                  </button>
                ))}
              </div>
            </div>

            <div
              className={`form-group ${formErrors.username ? "has-error" : ""}`}
            >
              <input
                type="text"
                name="username"
                className="form-control"
                placeholder="Username or Email"
                value={formData.username}
                onChange={handleInputChange}
                disabled={isSubmitting}
              />
              {formErrors.username && (
                <div className="error-message">
                  <span>⚠️</span>
                  {formErrors.username}
                </div>
              )}
            </div>

            <div
              className={`form-group ${formErrors.password ? "has-error" : ""}`}
            >
              <input
                type="password"
                name="password"
                className="form-control"
                placeholder="Password"
                value={formData.password}
                onChange={handleInputChange}
                disabled={isSubmitting}
              />
              {formErrors.password && (
                <div className="error-message">
                  <span>⚠️</span>
                  {formErrors.password}
                </div>
              )}
            </div>

            <div className="remember-forgot">
              <label className="remember-me">
                <input
                  type="checkbox"
                  name="rememberMe"
                  checked={formData.rememberMe}
                  onChange={handleInputChange}
                  disabled={isSubmitting}
                />
                Remember me
              </label>
              <a href="#forgot" className="forgot-password">
                Forgot password?
              </a>
            </div>

            <button
              type="submit"
              className="login-button"
              disabled={isSubmitting || isLoading}
            >
              {isSubmitting ? (
                <>
                  <div className="loading-spinner"></div>
                  Signing in...
                </>
              ) : (
                "Sign In"
              )}
            </button>
          </form>

          <div className="login-footer">Professional Admin Panel System</div>
        </div>
      </div>
    </div>
  );
};

export default Login;
