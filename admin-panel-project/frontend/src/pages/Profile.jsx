import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';

const Profile = () => {
  const { user, updateUser } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    username: user?.username || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleProfileUpdate = (e) => {
    e.preventDefault();
    // Profile update logic will be implemented here
    console.log('Profile update:', formData);
  };

  const handlePasswordChange = (e) => {
    e.preventDefault();
    // Password change logic will be implemented here
    console.log('Password change:', formData);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'superadmin':
        return '#ef4444';
      case 'admin':
        return '#3b82f6';
      case 'agent':
        return '#10b981';
      default:
        return '#64748b';
    }
  };

  return (
    <div style={{ padding: '2rem' }}>
      {/* Header */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-body">
          <div style={{ display: 'flex', alignItems: 'center', gap: '1.5rem' }}>
            <div
              style={{
                width: '80px',
                height: '80px',
                borderRadius: '50%',
                background: 'var(--primary-color)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '2rem',
                fontWeight: 'bold',
              }}
            >
              {user?.name?.charAt(0)?.toUpperCase()}
            </div>
            <div>
              <h1 style={{ margin: 0, marginBottom: '0.5rem' }}>{user?.name}</h1>
              <p style={{ margin: 0, color: 'var(--text-secondary)', marginBottom: '0.5rem' }}>
                {user?.email}
              </p>
              <span
                style={{
                  padding: '0.25rem 0.75rem',
                  borderRadius: '9999px',
                  fontSize: '0.75rem',
                  fontWeight: '500',
                  backgroundColor: getRoleColor(user?.role) + '20',
                  color: getRoleColor(user?.role),
                }}
              >
                {user?.role}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: '250px 1fr', gap: '2rem' }}>
        {/* Sidebar */}
        <div className="card">
          <div className="card-body" style={{ padding: '1rem' }}>
            <nav>
              <button
                onClick={() => setActiveTab('profile')}
                style={{
                  width: '100%',
                  padding: '0.75rem 1rem',
                  border: 'none',
                  background: activeTab === 'profile' ? 'var(--primary-color)' : 'transparent',
                  color: activeTab === 'profile' ? 'white' : 'var(--text-primary)',
                  borderRadius: '8px',
                  marginBottom: '0.5rem',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  textAlign: 'left',
                  transition: 'all 0.2s ease',
                }}
              >
                <span>👤</span>
                Profile Info
              </button>
              <button
                onClick={() => setActiveTab('password')}
                style={{
                  width: '100%',
                  padding: '0.75rem 1rem',
                  border: 'none',
                  background: activeTab === 'password' ? 'var(--primary-color)' : 'transparent',
                  color: activeTab === 'password' ? 'white' : 'var(--text-primary)',
                  borderRadius: '8px',
                  marginBottom: '0.5rem',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  textAlign: 'left',
                  transition: 'all 0.2s ease',
                }}
              >
                <span>🔒</span>
                Password
              </button>
              <button
                onClick={() => setActiveTab('activity')}
                style={{
                  width: '100%',
                  padding: '0.75rem 1rem',
                  border: 'none',
                  background: activeTab === 'activity' ? 'var(--primary-color)' : 'transparent',
                  color: activeTab === 'activity' ? 'white' : 'var(--text-primary)',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  textAlign: 'left',
                  transition: 'all 0.2s ease',
                }}
              >
                <span>📊</span>
                Activity
              </button>
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="card">
          <div className="card-body">
            {activeTab === 'profile' && (
              <div>
                <h3 style={{ marginBottom: '1.5rem' }}>Profile Information</h3>
                <form onSubmit={handleProfileUpdate}>
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                    <div className="form-group">
                      <label className="form-label">Full Name</label>
                      <input
                        type="text"
                        name="name"
                        className="form-control"
                        value={formData.name}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div className="form-group">
                      <label className="form-label">Username</label>
                      <input
                        type="text"
                        name="username"
                        className="form-control"
                        value={formData.username}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  <div className="form-group">
                    <label className="form-label">Email Address</label>
                    <input
                      type="email"
                      name="email"
                      className="form-control"
                      value={formData.email}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="form-group">
                    <label className="form-label">Role</label>
                    <input
                      type="text"
                      className="form-control"
                      value={user?.role}
                      disabled
                    />
                  </div>
                  <button type="submit" className="btn btn-primary">
                    Update Profile
                  </button>
                </form>
              </div>
            )}

            {activeTab === 'password' && (
              <div>
                <h3 style={{ marginBottom: '1.5rem' }}>Change Password</h3>
                <form onSubmit={handlePasswordChange}>
                  <div className="form-group">
                    <label className="form-label">Current Password</label>
                    <input
                      type="password"
                      name="currentPassword"
                      className="form-control"
                      value={formData.currentPassword}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="form-group">
                    <label className="form-label">New Password</label>
                    <input
                      type="password"
                      name="newPassword"
                      className="form-control"
                      value={formData.newPassword}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="form-group">
                    <label className="form-label">Confirm New Password</label>
                    <input
                      type="password"
                      name="confirmPassword"
                      className="form-control"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                    />
                  </div>
                  <button type="submit" className="btn btn-primary">
                    Change Password
                  </button>
                </form>
              </div>
            )}

            {activeTab === 'activity' && (
              <div>
                <h3 style={{ marginBottom: '1.5rem' }}>Account Activity</h3>
                <div style={{ display: 'grid', gap: '1rem' }}>
                  <div style={{ padding: '1rem', border: '1px solid var(--border-color)', borderRadius: '8px' }}>
                    <div style={{ fontWeight: '500', marginBottom: '0.5rem' }}>Account Created</div>
                    <div style={{ color: 'var(--text-secondary)' }}>
                      {user?.createdAt ? formatDate(user.createdAt) : 'N/A'}
                    </div>
                  </div>
                  <div style={{ padding: '1rem', border: '1px solid var(--border-color)', borderRadius: '8px' }}>
                    <div style={{ fontWeight: '500', marginBottom: '0.5rem' }}>Last Login</div>
                    <div style={{ color: 'var(--text-secondary)' }}>
                      {user?.lastLogin ? formatDate(user.lastLogin) : 'N/A'}
                    </div>
                  </div>
                  <div style={{ padding: '1rem', border: '1px solid var(--border-color)', borderRadius: '8px' }}>
                    <div style={{ fontWeight: '500', marginBottom: '0.5rem' }}>Account Status</div>
                    <div style={{ color: 'var(--success-color)' }}>
                      Active
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
