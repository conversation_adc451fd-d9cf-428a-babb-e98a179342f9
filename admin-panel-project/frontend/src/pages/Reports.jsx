import React from 'react';
import { useAuth } from '../context/AuthContext';

const Reports = () => {
  const { user } = useAuth();

  return (
    <div style={{ padding: '2rem' }}>
      {/* Header */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-header">
          <h1 className="card-title">Reports</h1>
        </div>
        <div className="card-body">
          <p style={{ color: 'var(--text-secondary)' }}>
            Generate and view various reports for your organization.
          </p>
        </div>
      </div>

      {/* Report Types */}
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1.5rem' }}>
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">User Reports</h3>
          </div>
          <div className="card-body">
            <p style={{ color: 'var(--text-secondary)', marginBottom: '1.5rem' }}>
              Generate reports about user activity, registrations, and engagement.
            </p>
            <button className="btn btn-primary">
              Generate User Report
            </button>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="card-title">System Reports</h3>
          </div>
          <div className="card-body">
            <p style={{ color: 'var(--text-secondary)', marginBottom: '1.5rem' }}>
              View system performance, uptime, and technical metrics.
            </p>
            <button className="btn btn-primary">
              Generate System Report
            </button>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Activity Reports</h3>
          </div>
          <div className="card-body">
            <p style={{ color: 'var(--text-secondary)', marginBottom: '1.5rem' }}>
              Track user activities, login patterns, and usage statistics.
            </p>
            <button className="btn btn-primary">
              Generate Activity Report
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Reports;
