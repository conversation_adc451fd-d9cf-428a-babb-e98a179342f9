import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';

const Settings = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('general');

  const tabs = [
    { id: 'general', label: 'General', icon: '⚙️' },
    { id: 'security', label: 'Security', icon: '🔒' },
    { id: 'notifications', label: 'Notifications', icon: '🔔' },
    { id: 'appearance', label: 'Appearance', icon: '🎨' },
  ];

  return (
    <div style={{ padding: '2rem' }}>
      {/* Header */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-header">
          <h1 className="card-title">Settings</h1>
        </div>
        <div className="card-body">
          <p style={{ color: 'var(--text-secondary)' }}>
            Manage your application settings and preferences.
          </p>
        </div>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: '250px 1fr', gap: '2rem' }}>
        {/* Sidebar */}
        <div className="card">
          <div className="card-body" style={{ padding: '1rem' }}>
            <nav>
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  style={{
                    width: '100%',
                    padding: '0.75rem 1rem',
                    border: 'none',
                    background: activeTab === tab.id ? 'var(--primary-color)' : 'transparent',
                    color: activeTab === tab.id ? 'white' : 'var(--text-primary)',
                    borderRadius: '8px',
                    marginBottom: '0.5rem',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.75rem',
                    textAlign: 'left',
                    transition: 'all 0.2s ease',
                  }}
                >
                  <span>{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="card">
          <div className="card-body">
            {activeTab === 'general' && (
              <div>
                <h3 style={{ marginBottom: '1.5rem' }}>General Settings</h3>
                <div className="form-group">
                  <label className="form-label">Application Name</label>
                  <input
                    type="text"
                    className="form-control"
                    defaultValue="Admin Panel"
                    disabled
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Time Zone</label>
                  <select className="form-control">
                    <option>UTC</option>
                    <option>America/New_York</option>
                    <option>Europe/London</option>
                    <option>Asia/Tokyo</option>
                  </select>
                </div>
                <div className="form-group">
                  <label className="form-label">Language</label>
                  <select className="form-control">
                    <option>English</option>
                    <option>Spanish</option>
                    <option>French</option>
                    <option>German</option>
                  </select>
                </div>
                <button className="btn btn-primary">Save Changes</button>
              </div>
            )}

            {activeTab === 'security' && (
              <div>
                <h3 style={{ marginBottom: '1.5rem' }}>Security Settings</h3>
                <div className="form-group">
                  <label className="form-label">Two-Factor Authentication</label>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                    <input type="checkbox" />
                    <span>Enable 2FA for enhanced security</span>
                  </div>
                </div>
                <div className="form-group">
                  <label className="form-label">Session Timeout (minutes)</label>
                  <input
                    type="number"
                    className="form-control"
                    defaultValue="30"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Password Policy</label>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                    <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      <input type="checkbox" defaultChecked />
                      Minimum 8 characters
                    </label>
                    <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      <input type="checkbox" defaultChecked />
                      Require uppercase letters
                    </label>
                    <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      <input type="checkbox" defaultChecked />
                      Require numbers
                    </label>
                  </div>
                </div>
                <button className="btn btn-primary">Save Changes</button>
              </div>
            )}

            {activeTab === 'notifications' && (
              <div>
                <h3 style={{ marginBottom: '1.5rem' }}>Notification Settings</h3>
                <div className="form-group">
                  <label className="form-label">Email Notifications</label>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                    <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      <input type="checkbox" defaultChecked />
                      New user registrations
                    </label>
                    <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      <input type="checkbox" defaultChecked />
                      System alerts
                    </label>
                    <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      <input type="checkbox" />
                      Weekly reports
                    </label>
                  </div>
                </div>
                <div className="form-group">
                  <label className="form-label">Push Notifications</label>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                    <input type="checkbox" defaultChecked />
                    <span>Enable browser notifications</span>
                  </div>
                </div>
                <button className="btn btn-primary">Save Changes</button>
              </div>
            )}

            {activeTab === 'appearance' && (
              <div>
                <h3 style={{ marginBottom: '1.5rem' }}>Appearance Settings</h3>
                <div className="form-group">
                  <label className="form-label">Theme</label>
                  <select className="form-control">
                    <option>Light</option>
                    <option>Dark</option>
                    <option>Auto</option>
                  </select>
                </div>
                <div className="form-group">
                  <label className="form-label">Sidebar Position</label>
                  <select className="form-control">
                    <option>Left</option>
                    <option>Right</option>
                  </select>
                </div>
                <div className="form-group">
                  <label className="form-label">Compact Mode</label>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                    <input type="checkbox" />
                    <span>Enable compact layout</span>
                  </div>
                </div>
                <button className="btn btn-primary">Save Changes</button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
