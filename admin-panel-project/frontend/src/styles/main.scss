// Variables
:root {
  --primary-color: #3b82f6;
  --primary-dark: #2563eb;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --background-color: #f8fafc;
  --surface-color: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --sidebar-bg: #1e293b;
  --sidebar-text: #cbd5e1;
  --sidebar-active: #3b82f6;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

// Reset and base styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
}

// Layout
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-body {
  flex: 1;
  display: flex;
  min-height: calc(100vh - 70px);
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

// Header
.header {
  background: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow);
  height: 70px;
  z-index: 1000;
  flex-shrink: 0;

  .header-container {
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 1rem;

    .sidebar-toggle-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border: none;
      background: transparent;
      color: var(--text-secondary);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: var(--background-color);
        color: var(--primary-color);
      }
    }
  }

  .header-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    color: var(--text-primary);

    .logo-icon {
      width: 40px;
      height: 40px;
      background: var(--primary-color);
      color: white;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 1.1rem;
    }

    .logo-text {
      font-size: 1.25rem;
    }
  }

// Sidebar
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  color: #cbd5e1;
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;

  &.collapsed {
    width: 80px;
  }

  .sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .sidebar-brand {
      display: flex;
      align-items: center;
      gap: 1rem;

      .brand-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, var(--primary-color), #2563eb);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      }

      .brand-text {
        display: flex;
        flex-direction: column;

        .brand-name {
          font-size: 1.125rem;
          font-weight: 700;
          color: white;
          line-height: 1.2;
        }

        .brand-subtitle {
          font-size: 0.75rem;
          color: #94a3b8;
          font-weight: 500;
        }
      }
    }
  }

  .sidebar-nav {
    flex: 1;
    padding: 1.5rem 0;
    overflow-y: auto;

    .nav-section {
      .nav-section-title {
        padding: 0 1.5rem 0.75rem;
        font-size: 0.75rem;
        font-weight: 600;
        color: #64748b;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .nav-list {
        list-style: none;
        padding: 0;
        margin: 0;

        .nav-item {
          margin: 0 1rem 0.5rem;

          .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.875rem 1rem;
            border-radius: 12px;
            text-decoration: none;
            color: #cbd5e1;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;

            &:hover {
              background: rgba(255, 255, 255, 0.08);
              color: white;
              transform: translateX(4px);
            }

            &.active {
              background: linear-gradient(135deg, var(--primary-color), #2563eb);
              color: white;
              box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

              .nav-indicator {
                opacity: 1;
              }
            }

            .nav-icon {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 20px;
              height: 20px;
              flex-shrink: 0;
            }

            .nav-label {
              font-size: 0.875rem;
              font-weight: 500;
              flex: 1;
            }

            .nav-indicator {
              width: 4px;
              height: 4px;
              background: white;
              border-radius: 50%;
              opacity: 0;
              transition: opacity 0.2s ease;
            }
          }
        }
      }
    }
  }

  .sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);

    .user-card {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 1rem;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 12px;
      margin-bottom: 1rem;

      .user-avatar {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, var(--primary-color), #2563eb);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        flex-shrink: 0;
      }

      .user-info {
        flex: 1;
        min-width: 0;

        .user-name {
          font-size: 0.875rem;
          font-weight: 600;
          color: white;
          line-height: 1.2;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .user-role {
          font-size: 0.75rem;
          color: #94a3b8;
          text-transform: capitalize;
          line-height: 1.2;
        }
      }
    }

    .footer-actions {
      display: flex;
      justify-content: center;

      .footer-action {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        text-decoration: none;
        color: #94a3b8;
        transition: all 0.2s ease;
        font-size: 0.875rem;

        &:hover {
          background: rgba(255, 255, 255, 0.08);
          color: white;
        }
      }
    }
  }
}

// Sidebar overlay for mobile
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 997;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.active {
    opacity: 1;
    visibility: visible;
  }
}

  .header-user {
    position: relative;

    .user-profile {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.5rem;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: var(--background-color);
      }

      .user-avatar {
        width: 40px;
        height: 40px;
        background: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 1rem;
      }

      .user-info {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .user-name {
          font-weight: 600;
          color: var(--text-primary);
          font-size: 0.875rem;
          line-height: 1.2;
        }

        .user-role {
          font-size: 0.75rem;
          font-weight: 500;
          text-transform: capitalize;
          line-height: 1.2;
        }
      }

      .dropdown-arrow {
        font-size: 0.75rem;
        color: var(--text-secondary);
        transition: transform 0.2s ease;
      }
    }

    .user-dropdown {
      position: absolute;
      top: 100%;
      right: 0;
      background: var(--surface-color);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      box-shadow: var(--shadow-lg);
      min-width: 180px;
      padding: 0.5rem 0;
      margin-top: 0.5rem;

      .dropdown-item {
        width: 100%;
        padding: 0.75rem 1rem;
        border: none;
        background: none;
        text-align: left;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        color: var(--text-primary);
        font-size: 0.875rem;
        transition: all 0.2s ease;

        &:hover {
          background: var(--background-color);
        }

        &.logout {
          color: var(--error-color);

          &:hover {
            background: rgba(239, 68, 68, 0.1);
          }
        }

        .dropdown-icon {
          font-size: 1rem;
        }
      }

      .dropdown-divider {
        height: 1px;
        background: var(--border-color);
        margin: 0.5rem 0;
      }
    }
  }

  .dropdown-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
  }
}

// Content area
.content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: var(--background-color);
}

// Cards
.card {
  background: var(--surface-color);
  border-radius: 12px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  overflow: hidden;

  .card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0;
    }
  }

  .card-body {
    padding: 1.5rem;
  }
}

// Buttons
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 0.5rem;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.btn-primary {
    background: var(--primary-color);
    color: white;

    &:hover:not(:disabled) {
      background: var(--primary-dark);
      transform: translateY(-1px);
    }
  }

  &.btn-secondary {
    background: var(--secondary-color);
    color: white;

    &:hover:not(:disabled) {
      background: #475569;
      transform: translateY(-1px);
    }
  }

  &.btn-success {
    background: var(--success-color);
    color: white;

    &:hover:not(:disabled) {
      background: #059669;
      transform: translateY(-1px);
    }
  }

  &.btn-danger {
    background: var(--error-color);
    color: white;

    &:hover:not(:disabled) {
      background: #dc2626;
      transform: translateY(-1px);
    }
  }

  &.btn-outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);

    &:hover:not(:disabled) {
      background: var(--primary-color);
      color: white;
    }
  }

  &.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }

  &.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
}

// Forms
.form-group {
  margin-bottom: 1.5rem;

  .form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
  }

  .form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--surface-color);

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    &.error {
      border-color: var(--error-color);
    }
  }

  .form-error {
    color: var(--error-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
  }
}

// Responsive
@media (max-width: 768px) {
  .app-body {
    position: relative;
  }

  .sidebar {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    z-index: 1000;
    transform: translateX(-100%);

    &.open {
      transform: translateX(0);
    }
  }

  .header {
    .header-container {
      padding: 0 1rem;
    }

    .header-logo .logo-text {
      display: none;
    }
  }

  .content {
    padding: 1rem;
  }

  .card {
    .card-body {
      padding: 1rem;
    }
  }
}

@media (max-width: 1024px) {
  .sidebar {
    &.collapsed {
      width: 60px;
    }
  }
}
